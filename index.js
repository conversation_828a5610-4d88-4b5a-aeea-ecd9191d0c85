// ai-twitter-bot/index.mjs
import dotenv from 'dotenv';
dotenv.config();

import Parser from 'rss-parser';
import fetch from 'node-fetch';
import unfluff from 'unfluff';
import { TwitterApi } from 'twitter-api-v2';
import OpenAI from 'openai';
import fs from 'fs';
import cron from 'node-cron';

const POSTED_FILE = './posted.json';
const postedLinks = new Set(
  fs.existsSync(POSTED_FILE) ? JSON.parse(fs.readFileSync(POSTED_FILE, 'utf8')) : []
);

const parser = new Parser();

const twitterClient = new TwitterApi({
  appKey: process.env.TWITTER_APP_KEY,
  appSecret: process.env.TWITTER_APP_SECRET,
  accessToken: process.env.TWITTER_ACCESS_TOKEN,
  accessSecret: process.env.TWITTER_ACCESS_SECRET,
});

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

const feeds = [
  'https://techcrunch.com/feed/',
  'https://www.theverge.com/rss/index.xml',
  'https://news.crunchbase.com/feed/',
  'https://www.androidpolice.com/feed/',
];

async function extractContent(url) {
  try {
    const res = await fetch(url);
    const html = await res.text();
    const article = unfluff(html);
    return article.text;
  } catch (err) {
    console.error(`Failed to fetch content from ${url}:`, err);
    return null;
  }
}

async function processFeed() {
  for (const feedUrl of feeds) {
    const feed = await parser.parseURL(feedUrl);
    for (const entry of feed.items) {
      if (postedLinks.has(entry.link)) continue;

      const content = await extractContent(entry.link);
      
      if (!content) continue;

      const prompt = `Here is a tech article:\n\nTitle: ${entry.title}\n\nContent: ${content.slice(0, 3000)}\n\nWrite a tweet (max 280 characters) summarizing the article with a clear, smart POV. Use an analytical and insightful tone.`;
    // console.log("prompt", prompt)
    //   try {
    //     const completion = await openai.chat.completions.create({
    //       model: 'gpt-4o',
    //       messages: [{ role: 'user', content: prompt }],
    //       max_tokens: 200,
    //     });

    //     const tweet = completion.choices[0].message.content.trim();
    //      console.log(`✅ tweet: ${tweet}`);
    //     if (tweet.length > 280 || !tweet) continue;

    //     // await twitterClient.v2.tweet(tweet);
    //     console.log(`✅ Posted: ${entry.title}`);
    //     // postedLinks.add(entry.link);
    //     // fs.writeFileSync(POSTED_FILE, JSON.stringify([...postedLinks], null, 2));
    //     break; // Limit to 1 post per run
    //   } catch (err) {
    //     console.error(`❌ Error processing ${entry.link}`, err);
    //   }
    }
  }
}
console.log('Attempting tweet with:', {
  appKey: process.env.TWITTER_APP_KEY,
  appSecret: process.env.TWITTER_APP_SECRET?.slice(0, 4) + '***',
  accessToken: process.env.TWITTER_ACCESS_TOKEN?.slice(0, 4) + '***',
  accessSecret: process.env.TWITTER_ACCESS_SECRET?.slice(0, 4) + '***',
});
// cron.schedule('*/30 * * * *', processFeed);
processFeed();
