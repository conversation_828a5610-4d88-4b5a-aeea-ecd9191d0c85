'use client'

import { useEffect, useState } from 'react'
import { Arrow<PERSON>eft, Plus, Trash2, RefreshCw, ExternalLink, Calendar, Wand2, Send, Edit3 } from 'lucide-react'
import Link from 'next/link'

interface RSSFeed {
  id: string
  name: string
  url: string
  is_active: boolean
  created_at: string
  updated_at: string
}

interface FeedItem {
  title: string
  url: string
  publishedAt: string
  content: string
  categories: string[]
  author?: string
  score: number
}

interface GeneratedContent {
  shortHook: string
  longHook: string
  personalTouch: string
  tweetContent: string
  hashtags: string[]
}

interface EditingContent {
  item: FeedItem
  generated?: GeneratedContent
  customContent: string
}

// Utility function to strip HTML tags
const stripHtml = (html: string): string => {
  return html.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').trim()
}

export default function FeedsPage() {
  const [feeds, setFeeds] = useState<RSSFeed[]>([])
  const [feedItems, setFeedItems] = useState<FeedItem[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newFeed, setNewFeed] = useState({ name: '', url: '' })
  const [editingContent, setEditingContent] = useState<EditingContent | null>(null)
  const [generating, setGenerating] = useState<Set<string>>(new Set())
  const [posting, setPosting] = useState<Set<string>>(new Set())

  const fetchFeeds = async () => {
    try {
      const response = await fetch('/api/rss/feeds')
      if (response.ok) {
        const data = await response.json()
        setFeeds(data)
      }
    } catch (error) {
      console.error('Error fetching feeds:', error)
    }
  }

  const fetchFeedItems = async () => {
    try {
      setRefreshing(true)
      const response = await fetch('/api/rss/items')
      if (response.ok) {
        const data = await response.json()
        setFeedItems(data)
      }
    } catch (error) {
      console.error('Error fetching feed items:', error)
    } finally {
      setRefreshing(false)
    }
  }

  const addFeed = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await fetch('/api/rss/feeds', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newFeed)
      })
      
      if (response.ok) {
        setNewFeed({ name: '', url: '' })
        setShowAddForm(false)
        await fetchFeeds()
      }
    } catch (error) {
      console.error('Error adding feed:', error)
    }
  }

  const toggleFeed = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/rss/feeds/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: !isActive })
      })
      
      if (response.ok) {
        await fetchFeeds()
      }
    } catch (error) {
      console.error('Error toggling feed:', error)
    }
  }

  const deleteFeed = async (id: string) => {
    if (!confirm('Are you sure you want to delete this feed?')) return

    try {
      const response = await fetch(`/api/rss/feeds/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchFeeds()
      }
    } catch (error) {
      console.error('Error deleting feed:', error)
    }
  }

  const generateContent = async (item: FeedItem) => {
    const itemKey = `${item.url}-${item.title}`

    try {
      setGenerating(prev => new Set([...prev, itemKey]))

      const response = await fetch('/api/content/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: item.title,
          content: stripHtml(item.content),
          url: item.url
        })
      })

      if (response.ok) {
        const generatedContent: GeneratedContent = await response.json()

        // Open editing modal with generated content
        setEditingContent({
          item,
          generated: generatedContent,
          customContent: generatedContent.tweetContent
        })
      }
    } catch (error) {
      console.error('Error generating content:', error)
      alert('Error generating content. Please try again.')
    } finally {
      setGenerating(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemKey)
        return newSet
      })
    }
  }

  const openEditModal = (item: FeedItem) => {
    setEditingContent({
      item,
      customContent: `${item.title}\n\n${item.url}`
    })
  }

  const postContent = async (content: string, originalUrl: string, originalTitle: string) => {
    const itemKey = `${originalUrl}-${originalTitle}`

    try {
      setPosting(prev => new Set([...prev, itemKey]))

      // First add to content queue
      const queueResponse = await fetch('/api/content/queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          original_url: originalUrl,
          original_title: originalTitle,
          original_content: stripHtml(editingContent?.item.content || ''),
          ai_generated_content: content,
          priority_score: editingContent?.item.score || 50
        })
      })

      if (queueResponse.ok) {
        const queueItem = await queueResponse.json()

        // Then post to Twitter
        const postResponse = await fetch('/api/content/post', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contentIds: [queueItem.id]
          })
        })

        if (postResponse.ok) {
          alert('Content posted successfully!')
          setEditingContent(null)
        } else {
          alert('Content added to queue but failed to post. You can post it later from the Content page.')
          setEditingContent(null)
        }
      }
    } catch (error) {
      console.error('Error posting content:', error)
      alert('Error posting content. Please try again.')
    } finally {
      setPosting(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemKey)
        return newSet
      })
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([fetchFeeds(), fetchFeedItems()])
      setLoading(false)
    }
    loadData()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading RSS feeds...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <Link href="/" className="mr-4">
                <ArrowLeft className="h-6 w-6 text-gray-600 hover:text-gray-900" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">RSS Feeds</h1>
                <p className="text-sm text-gray-600 mt-1">
                  Manage your RSS feeds and view aggregated content
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={fetchFeedItems}
                disabled={refreshing}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh Content
              </button>
              <button
                type="button"
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Feed
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* RSS Feeds List */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Configured Feeds</h2>
                <p className="text-sm text-gray-600 mt-1">{feeds.length} feeds configured</p>
              </div>
              <div className="divide-y divide-gray-200">
                {feeds.map((feed) => (
                  <div key={feed.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {feed.name}
                        </h3>
                        <p className="text-xs text-gray-500 truncate mt-1">
                          {feed.url}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          type="button"
                          onClick={() => toggleFeed(feed.id, feed.is_active)}
                          className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                            feed.is_active ? 'bg-blue-600' : 'bg-gray-200'
                          }`}
                        >
                          <span
                            className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                              feed.is_active ? 'translate-x-5' : 'translate-x-0'
                            }`}
                          />
                        </button>
                        <button
                          type="button"
                          onClick={() => deleteFeed(feed.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete feed"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Feed Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Latest Content</h2>
                <p className="text-sm text-gray-600 mt-1">
                  {feedItems.length} items found (deduplicated and scored)
                </p>
              </div>
              <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                {feedItems.map((item, index) => (
                  <div key={index} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 mb-2">
                          {item.title}
                        </h3>
                        <div className="flex items-center text-xs text-gray-500 space-x-4 mb-2">
                          <span className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(item.publishedAt).toLocaleDateString()}
                          </span>
                          {item.author && <span>by {item.author}</span>}
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            Score: {item.score.toFixed(0)}
                          </span>
                        </div>
                        {item.categories.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-2">
                            {item.categories.slice(0, 3).map((category, idx) => (
                              <span
                                key={idx}
                                className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"
                              >
                                {category}
                              </span>
                            ))}
                          </div>
                        )}
                        <p className="text-xs text-gray-600 line-clamp-2 mb-3">
                          {stripHtml(item.content).slice(0, 200)}...
                        </p>

                        {/* Action Buttons */}
                        <div className="flex items-center space-x-2 mb-2">
                          <button
                            type="button"
                            onClick={() => generateContent(item)}
                            disabled={generating.has(`${item.url}-${item.title}`)}
                            className="inline-flex items-center px-2 py-1 border border-blue-300 rounded text-xs font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                          >
                            <Wand2 className={`h-3 w-3 mr-1 ${generating.has(`${item.url}-${item.title}`) ? 'animate-spin' : ''}`} />
                            {generating.has(`${item.url}-${item.title}`) ? 'Generating...' : 'Generate'}
                          </button>
                          <button
                            type="button"
                            onClick={() => openEditModal(item)}
                            className="inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            <Edit3 className="h-3 w-3 mr-1" />
                            Edit
                          </button>
                        </div>
                      </div>
                      <a
                        href={item.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="ml-4 text-blue-600 hover:text-blue-900"
                        title="View original article"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Feed Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add RSS Feed</h3>
              <form onSubmit={addFeed}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Feed Name
                  </label>
                  <input
                    type="text"
                    value={newFeed.name}
                    onChange={(e) => setNewFeed({ ...newFeed, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter feed name"
                    title="Feed name"
                    required
                  />
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Feed URL
                  </label>
                  <input
                    type="url"
                    value={newFeed.url}
                    onChange={(e) => setNewFeed({ ...newFeed, url: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="https://example.com/feed.xml"
                    title="Feed URL"
                    required
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    Add Feed
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Content Editing Modal */}
      {editingContent && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Prepare Content for Twitter
              </h3>

              {/* Original Article Info */}
              <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Original Article:</h4>
                <p className="text-sm text-gray-700 mb-2">{editingContent.item.title}</p>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {stripHtml(editingContent.item.content).slice(0, 300)}...
                </p>
              </div>

              {/* Generated Content (if available) */}
              {editingContent.generated && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">AI Generated Options:</h4>

                  <div className="space-y-2 mb-3">
                    <div className="p-2 bg-blue-50 rounded border">
                      <span className="text-xs font-medium text-blue-700">Short Hook: </span>
                      <span className="text-xs text-blue-600">{editingContent.generated.shortHook}</span>
                    </div>
                    <div className="p-2 bg-blue-50 rounded border">
                      <span className="text-xs font-medium text-blue-700">Long Hook: </span>
                      <span className="text-xs text-blue-600">{editingContent.generated.longHook}</span>
                    </div>
                    <div className="p-2 bg-blue-50 rounded border">
                      <span className="text-xs font-medium text-blue-700">Personal Touch: </span>
                      <span className="text-xs text-blue-600">{editingContent.generated.personalTouch}</span>
                    </div>
                  </div>

                  <div className="mb-3">
                    <span className="text-xs font-medium text-gray-700">Suggested Hashtags: </span>
                    <span className="text-xs text-gray-600">
                      {editingContent.generated.hashtags.map(tag => `#${tag}`).join(' ')}
                    </span>
                  </div>
                </div>
              )}

              {/* Editable Content */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tweet Content (Max 280 characters):
                </label>
                <textarea
                  value={editingContent.customContent}
                  onChange={(e) => setEditingContent({
                    ...editingContent,
                    customContent: e.target.value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  rows={4}
                  maxLength={280}
                  placeholder="Edit your tweet content here..."
                />
                <div className="text-right text-xs text-gray-500 mt-1">
                  {editingContent.customContent.length}/280 characters
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setEditingContent(null)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={() => postContent(
                    editingContent.customContent,
                    editingContent.item.url,
                    editingContent.item.title
                  )}
                  disabled={posting.has(`${editingContent.item.url}-${editingContent.item.title}`) || !editingContent.customContent.trim()}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  <Send className={`h-4 w-4 mr-2 ${posting.has(`${editingContent.item.url}-${editingContent.item.title}`) ? 'animate-spin' : ''}`} />
                  {posting.has(`${editingContent.item.url}-${editingContent.item.title}`) ? 'Posting...' : 'Post to Twitter'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
