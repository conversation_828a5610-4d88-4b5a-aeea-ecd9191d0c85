import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'

export async function GET() {
  try {
    const contentQueue = await dbOperations.getContentQueue(50)
    return NextResponse.json(contentQueue)
  } catch (error) {
    console.error('Error fetching content queue:', error)
    return NextResponse.json(
      { error: 'Failed to fetch content queue' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const contentData = await request.json()

    if (!contentData.original_url || !contentData.original_title) {
      return NextResponse.json(
        { error: 'Original URL and title are required' },
        { status: 400 }
      )
    }

    const newContentItem = await dbOperations.addToContentQueue({
      original_url: contentData.original_url,
      original_title: contentData.original_title,
      original_content: contentData.original_content || '',
      ai_generated_content: contentData.ai_generated_content,
      short_hook: contentData.short_hook,
      long_hook: contentData.long_hook,
      personal_touch: contentData.personal_touch,
      rss_feed_id: contentData.rss_feed_id,
      is_selected: contentData.is_selected || false,
      is_posted: false,
      priority_score: contentData.priority_score || 50
    })

    return NextResponse.json(newContentItem)
  } catch (error) {
    console.error('Error adding to content queue:', error)
    return NextResponse.json(
      { error: 'Failed to add to content queue' },
      { status: 500 }
    )
  }
}
