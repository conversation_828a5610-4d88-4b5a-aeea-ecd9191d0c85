import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Client for browser/client-side operations
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Admin client for server-side operations
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

// Database types
export interface RSSFeed {
  id: string
  name: string
  url: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface PostedTweet {
  id: string
  tweet_id?: string
  content: string
  original_url?: string
  original_title?: string
  rss_feed_id?: string
  impressions: number
  retweets: number
  likes: number
  replies: number
  posted_at: string
  created_at: string
}

export interface TwitterAnalytics {
  id: string
  followers_count: number
  following_count: number
  total_tweets: number
  total_impressions: number
  total_engagements: number
  recorded_at: string
}

export interface UserPreferences {
  id: string
  max_topics_to_select: number
  posting_interval_minutes: number
  ai_tone: string
  include_personal_touch: boolean
  auto_post_enabled: boolean
  created_at: string
  updated_at: string
}

export interface TwitterTokens {
  id: string
  access_token: string
  refresh_token: string
  expires_at?: string
  token_type: string
  scope?: string
  created_at: string
  updated_at: string
}

export interface ContentQueue {
  id: string
  original_url: string
  original_title?: string
  original_content?: string
  ai_generated_content?: string
  short_hook?: string
  long_hook?: string
  personal_touch?: string
  rss_feed_id?: string
  is_selected: boolean
  is_posted: boolean
  priority_score: number
  created_at: string
  updated_at: string
}

// Database operations
export const dbOperations = {
  // RSS Feeds
  async getRSSFeeds() {
    const { data, error } = await supabase
      .from('rss_feeds')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data as RSSFeed[]
  },

  async addRSSFeed(name: string, url: string) {
    const { data, error } = await supabase
      .from('rss_feeds')
      .insert({ name, url })
      .select()
      .single()
    
    if (error) throw error
    return data as RSSFeed
  },

  async updateRSSFeed(id: string, updates: Partial<RSSFeed>) {
    const { data, error } = await supabase
      .from('rss_feeds')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data as RSSFeed
  },

  // Posted Tweets
  async getPostedTweets(limit = 50) {
    const { data, error } = await supabase
      .from('posted_tweets')
      .select('*')
      .order('posted_at', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data as PostedTweet[]
  },

  async addPostedTweet(tweet: Omit<PostedTweet, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('posted_tweets')
      .insert(tweet)
      .select()
      .single()
    
    if (error) throw error
    return data as PostedTweet
  },

  // Twitter Analytics
  async getLatestAnalytics() {
    const { data, error } = await supabase
      .from('twitter_analytics')
      .select('*')
      .order('recorded_at', { ascending: false })
      .limit(1)
      .single()
    
    if (error && error.code !== 'PGRST116') throw error
    return data as TwitterAnalytics | null
  },

  async addAnalytics(analytics: Omit<TwitterAnalytics, 'id' | 'recorded_at'>) {
    const { data, error } = await supabase
      .from('twitter_analytics')
      .insert(analytics)
      .select()
      .single()
    
    if (error) throw error
    return data as TwitterAnalytics
  },

  // User Preferences
  async getUserPreferences() {
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .limit(1)
      .single()
    
    if (error && error.code !== 'PGRST116') throw error
    return data as UserPreferences | null
  },

  async updateUserPreferences(updates: Partial<UserPreferences>) {
    const { data, error } = await supabase
      .from('user_preferences')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .select()
      .single()
    
    if (error) throw error
    return data as UserPreferences
  },

  // Content Queue
  async getContentQueue(limit = 20) {
    const { data, error } = await supabase
      .from('content_queue')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data as ContentQueue[]
  },

  async addToContentQueue(content: Omit<ContentQueue, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('content_queue')
      .insert(content)
      .select()
      .single()
    
    if (error) throw error
    return data as ContentQueue
  },

  async updateContentQueue(id: string, updates: Partial<ContentQueue>) {
    const { data, error } = await supabase
      .from('content_queue')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as ContentQueue
  },

  async getSelectedContent() {
    const { data, error } = await supabase
      .from('content_queue')
      .select('*')
      .eq('is_selected', true)
      .eq('is_posted', false)
      .order('priority_score', { ascending: false })

    if (error) throw error
    return data as ContentQueue[]
  },

  async markContentAsPosted(ids: string[]) {
    const { data, error } = await supabase
      .from('content_queue')
      .update({
        is_posted: true,
        is_selected: false,
        updated_at: new Date().toISOString()
      })
      .in('id', ids)
      .select()

    if (error) throw error
    return data as ContentQueue[]
  },

  // Delete RSS Feed
  async deleteRSSFeed(id: string) {
    const { error } = await supabase
      .from('rss_feeds')
      .delete()
      .eq('id', id)

    if (error) throw error
    return true
  },

  // Twitter Tokens Management
  async getTwitterTokens(): Promise<TwitterTokens | null> {
    const { data, error } = await supabase
      .from('twitter_tokens')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') throw error
    return data as TwitterTokens | null
  },

  async saveTwitterTokens(tokens: Omit<TwitterTokens, 'id' | 'created_at' | 'updated_at'>): Promise<TwitterTokens> {
    // First, try to update existing tokens
    const { data: existingData } = await supabase
      .from('twitter_tokens')
      .select('id')
      .limit(1)
      .single()

    if (existingData) {
      // Update existing tokens
      const { data, error } = await supabase
        .from('twitter_tokens')
        .update({
          ...tokens,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingData.id)
        .select()
        .single()

      if (error) throw error
      return data as TwitterTokens
    } else {
      // Insert new tokens
      const { data, error } = await supabase
        .from('twitter_tokens')
        .insert({
          ...tokens,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error
      return data as TwitterTokens
    }
  },

  async updateTwitterTokens(accessToken: string, refreshToken: string, expiresAt?: string): Promise<TwitterTokens> {
    const { data: existingData } = await supabase
      .from('twitter_tokens')
      .select('id')
      .limit(1)
      .single()

    if (!existingData) {
      throw new Error('No existing Twitter tokens found. Please authenticate first.')
    }

    const { data, error } = await supabase
      .from('twitter_tokens')
      .update({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_at: expiresAt,
        updated_at: new Date().toISOString()
      })
      .eq('id', existingData.id)
      .select()
      .single()

    if (error) throw error
    return data as TwitterTokens
  }
}
