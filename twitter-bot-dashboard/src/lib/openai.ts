import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!
})

export interface ContentGenerationOptions {
  tone?: 'analytical' | 'casual' | 'professional' | 'enthusiastic' | 'critical'
  includePersonalTouch?: boolean
  maxLength?: number
  includeHashtags?: boolean
}

export interface GeneratedContent {
  shortHook: string
  longHook: string
  personalTouch: string
  tweetContent: string
  hashtags: string[]
}

export const aiOperations = {
  // Generate tweet content with hooks and personal touch
  async generateTweetContent(
    title: string,
    content: string,
    url: string,
    options: ContentGenerationOptions = {}
  ): Promise<GeneratedContent> {
    const {
      tone = 'analytical',
      includePersonalTouch = true,
      maxLength = 280
    } = options

    try {
      const prompt = `
You are an expert social media content creator specializing in tech and startup content. 
Create engaging Twitter content based on this article:

Title: ${title}
Content: ${content.slice(0, 2000)}
URL: ${url}

Requirements:
1. Generate a SHORT HOOK (1-2 sentences, max 50 characters) that grabs attention
2. Generate a LONG HOOK (2-3 sentences, max 100 characters) that provides more context
3. Generate a PERSONAL TOUCH message that adds your unique perspective or insight
4. Create the final TWEET CONTENT (max ${maxLength} characters) combining the best elements
5. Suggest relevant hashtags

Tone: ${tone}
Include personal perspective: ${includePersonalTouch}

IMPORTANT: Respond with ONLY a valid JSON object. Do not include markdown code blocks, explanations, or any other text. Just the raw JSON.

Format your response exactly like this:
{
  "shortHook": "...",
  "longHook": "...",
  "personalTouch": "...",
  "tweetContent": "...",
  "hashtags": ["hashtag1", "hashtag2", "hashtag3"]
}

Best practices for maximum engagement:
- Start with a compelling hook
- Use numbers, statistics, or surprising facts when available
- Ask questions or create curiosity gaps
- Include actionable insights
- Use power words and emotional triggers
- Keep it conversational and authentic
- Add your unique perspective or hot take
`

      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 500,
        temperature: 0.7
      })

      const response = completion.choices[0].message.content?.trim()
      if (!response) throw new Error('No response from OpenAI')

      console.log('OpenAI raw response:', response.substring(0, 500) + (response.length > 500 ? '...' : ''))

      // Extract JSON from response (handle markdown code blocks)
      let parsedResponse
      try {
        parsedResponse = this.extractAndParseJSON(response)
      } catch (parseError) {
        console.error('JSON parsing failed completely, creating fallback from raw response')
        // If JSON parsing fails completely, try to extract content manually
        parsedResponse = this.extractContentFromRawResponse(response, title, url)
      }

      // Validate and clean up the response
      return {
        shortHook: parsedResponse.shortHook || '',
        longHook: parsedResponse.longHook || '',
        personalTouch: parsedResponse.personalTouch || '',
        tweetContent: parsedResponse.tweetContent || '',
        hashtags: Array.isArray(parsedResponse.hashtags) ? parsedResponse.hashtags : []
      }
    } catch (error) {
      console.error('Error generating tweet content:', error)
      
      // Fallback: generate simple content
      return this.generateFallbackContent(title, content, url, options)
    }
  },

  // Generate multiple variations of tweet content
  async generateTweetVariations(
    title: string,
    content: string,
    url: string,
    count: number = 3,
    options: ContentGenerationOptions = {}
  ): Promise<GeneratedContent[]> {
    const variations: GeneratedContent[] = []
    
    for (let i = 0; i < count; i++) {
      try {
        const variation = await this.generateTweetContent(title, content, url, {
          ...options,
          tone: ['analytical', 'casual', 'enthusiastic'][i % 3] as 'analytical' | 'casual' | 'enthusiastic'
        })
        variations.push(variation)
      } catch (error) {
        console.error(`Error generating variation ${i + 1}:`, error)
      }
    }
    
    return variations
  },

  // Generate content summary for preview
  async generateContentSummary(title: string, content: string): Promise<string> {
    try {
      const prompt = `
Summarize this article in 2-3 sentences, focusing on the key insights and takeaways:

Title: ${title}
Content: ${content.slice(0, 1500)}

Make it engaging and highlight what makes this newsworthy or interesting.
`

      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 150,
        temperature: 0.5
      })

      return completion.choices[0].message.content?.trim() || 'Summary not available'
    } catch (error) {
      console.error('Error generating summary:', error)
      return 'Summary not available'
    }
  },

  // Analyze content for engagement potential
  async analyzeEngagementPotential(title: string, content: string): Promise<{
    score: number
    factors: string[]
    suggestions: string[]
  }> {
    try {
      const prompt = `
Analyze this content for Twitter engagement potential:

Title: ${title}
Content: ${content.slice(0, 1000)}

Rate the engagement potential (1-10) and provide:
1. Key factors that make it engaging or not
2. Suggestions to improve engagement

IMPORTANT: Respond with ONLY a valid JSON object. Do not include markdown code blocks or explanations.

Format exactly like this:
{
  "score": 7,
  "factors": ["factor1", "factor2"],
  "suggestions": ["suggestion1", "suggestion2"]
}
`

      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 300,
        temperature: 0.3
      })

      const response = completion.choices[0].message.content?.trim()
      if (!response) throw new Error('No response from OpenAI')

      return this.extractAndParseJSON(response)
    } catch (error) {
      console.error('Error analyzing engagement potential:', error)
      return {
        score: 5,
        factors: ['Unable to analyze'],
        suggestions: ['Try again later']
      }
    }
  },

  // Extract and parse JSON from OpenAI response (handles markdown code blocks)
  extractAndParseJSON(response: string): any {
    console.log('Attempting to parse response:', response.substring(0, 200) + (response.length > 200 ? '...' : ''))

    try {
      // First try direct JSON parsing
      const parsed = JSON.parse(response)
      console.log('Successfully parsed JSON directly')
      return parsed
    } catch (directError) {
      console.log('Direct JSON parsing failed, trying to extract from markdown...')

      // If that fails, try to extract JSON from markdown code blocks
      const jsonMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/)
      if (jsonMatch && jsonMatch[1]) {
        try {
          const parsed = JSON.parse(jsonMatch[1])
          console.log('Successfully parsed JSON from markdown code block')
          return parsed
        } catch (parseError) {
          console.error('Error parsing extracted JSON from markdown:', parseError)
        }
      }

      // Try to find JSON object without code blocks
      const objectMatch = response.match(/\{[\s\S]*\}/)
      if (objectMatch) {
        try {
          const parsed = JSON.parse(objectMatch[0])
          console.log('Successfully parsed JSON object found in response')
          return parsed
        } catch (parseError) {
          console.error('Error parsing found JSON object:', parseError)
        }
      }

      // If all parsing fails, provide detailed error information
      console.error('All JSON parsing attempts failed. Original error:', directError)
      console.error('Full response:', response)
      throw new Error(`Failed to parse JSON from response. Response starts with: "${response.substring(0, 100)}..."`)
    }
  },

  // Extract content from raw response when JSON parsing fails
  extractContentFromRawResponse(response: string, title: string, url: string): GeneratedContent {
    console.log('Extracting content from raw response manually')

    // Try to extract content using patterns
    const lines = response.split('\n').map(line => line.trim()).filter(line => line.length > 0)

    let shortHook = ''
    let longHook = ''
    let personalTouch = ''
    let tweetContent = ''
    let hashtags: string[] = []

    // Look for content patterns
    for (const line of lines) {
      if (line.toLowerCase().includes('short') && line.includes(':')) {
        shortHook = line.split(':').slice(1).join(':').trim().replace(/['"]/g, '')
      } else if (line.toLowerCase().includes('long') && line.includes(':')) {
        longHook = line.split(':').slice(1).join(':').trim().replace(/['"]/g, '')
      } else if (line.toLowerCase().includes('personal') && line.includes(':')) {
        personalTouch = line.split(':').slice(1).join(':').trim().replace(/['"]/g, '')
      } else if (line.toLowerCase().includes('tweet') && line.includes(':')) {
        tweetContent = line.split(':').slice(1).join(':').trim().replace(/['"]/g, '')
      } else if (line.includes('#')) {
        // Extract hashtags
        const hashtagMatches = line.match(/#\w+/g)
        if (hashtagMatches) {
          hashtags.push(...hashtagMatches.map(tag => tag.toLowerCase()))
        }
      }
    }

    // If we couldn't extract proper content, create fallback
    if (!shortHook || !tweetContent) {
      return this.generateFallbackContent(title, '', url)
    }

    // Ensure tweet content includes URL if not present
    if (!tweetContent.includes(url)) {
      tweetContent = `${tweetContent} ${url}`.trim()
    }

    return {
      shortHook: shortHook || title.slice(0, 50),
      longHook: longHook || `Interesting read: ${title}`,
      personalTouch: personalTouch || 'Worth checking out this perspective.',
      tweetContent: tweetContent,
      hashtags: hashtags.length > 0 ? hashtags.slice(0, 3) : ['tech', 'news']
    }
  },

  // Fallback content generation when AI fails
  generateFallbackContent(
    title: string,
    content: string,
    url: string,
    options: ContentGenerationOptions = {}
  ): GeneratedContent {
    const shortHook = title.slice(0, 50) + (title.length > 50 ? '...' : '')
    const longHook = `Interesting read: ${title.slice(0, 80)}${title.length > 80 ? '...' : ''}`
    const personalTouch = "Worth checking out this perspective on the latest tech developments."

    // Create basic tweet content
    const baseContent = `${shortHook}\n\n${personalTouch}\n\n${url}`
    const tweetContent = baseContent.length > (options.maxLength || 280)
      ? `${title.slice(0, 200)}...\n\n${url}`
      : baseContent

    return {
      shortHook,
      longHook,
      personalTouch,
      tweetContent,
      hashtags: ['tech', 'news', 'startup']
    }
  }
}

export default aiOperations
