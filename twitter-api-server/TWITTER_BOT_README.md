# Twitter Bot Implementation

This document describes the comprehensive Twitter bot implementation that automatically generates and posts engaging tweets from tech articles using AI.

## Architecture Overview

The Twitter bot is built using a modular NestJS architecture with the following components:

### Core Services

1. **ArticleFetcherService** - Fetches and extracts content from web articles
2. **ArticleSummarizerService** - Summarizes articles using GPT-3.5 Turbo
3. **TweetGeneratorService** - Generates engaging tweets using GPT-4
4. **TweetSchedulerService** - Orchestrates the complete pipeline with cron scheduling

### Key Features

- **Cost-Optimized AI Usage**: GPT-3.5 for summarization (~$0.001-0.002 per call), GPT-4 for tweet generation (~$0.03 per tweet)
- **Chain-of-Thought Prompting**: Advanced prompting strategy for high-quality, engaging tweets
- **Robust Error Handling**: Comprehensive retry logic and fallback mechanisms
- **Token Usage Tracking**: Detailed logging for cost monitoring
- **Content Filtering**: Validates article quality and tweet length
- **Duplicate Prevention**: Tracks posted URLs to avoid duplicates

## API Endpoints

### Bot Operations

- `POST /twitter/bot/fetch-article` - Fetch and extract article content
- `POST /twitter/bot/summarize-article` - Summarize article with GPT-3.5
- `POST /twitter/bot/generate-tweet` - Generate tweet with GPT-4
- `POST /twitter/bot/generate-and-post-tweet` - Complete pipeline: fetch → summarize → generate → post

### Scheduler Management

- `POST /twitter/bot/scheduler/start` - Start automated tweet scheduling
- `POST /twitter/bot/scheduler/stop` - Stop all schedulers
- `GET /twitter/bot/scheduler/status` - Get scheduler status
- `POST /twitter/bot/scheduler/trigger` - Manually trigger tweet generation

## Configuration

### Environment Variables

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Twitter API Configuration
TWITTER_CLIENT_ID=your_twitter_client_id_here
TWITTER_CLIENT_SECRET=your_twitter_client_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_REFRESH_TOKEN=your_twitter_refresh_token_here

# Bot Configuration
TWITTER_BOT_ENABLED=false
TWITTER_BOT_CRON_SCHEDULE=0 */2 * * *
TWITTER_BOT_MAX_TWEETS_PER_RUN=1
TWITTER_BOT_FEED_URLS=https://techcrunch.com/feed/,https://www.theverge.com/rss/index.xml
```

### Default RSS Feeds

- TechCrunch: `https://techcrunch.com/feed/`
- The Verge: `https://www.theverge.com/rss/index.xml`
- Hacker News: `https://news.ycombinator.com/rss`
- O'Reilly Radar: `https://feeds.feedburner.com/oreilly/radar`

## Usage Examples

### Manual Tweet Generation

```bash
# Fetch and analyze an article
curl -X POST http://localhost:3001/twitter/bot/fetch-article \
  -H "Content-Type: application/json" \
  -d '{"url": "https://techcrunch.com/2023/01/01/ai-development"}'

# Generate and post a tweet
curl -X POST http://localhost:3001/twitter/bot/generate-and-post-tweet \
  -H "Content-Type: application/json" \
  -d '{"url": "https://techcrunch.com/2023/01/01/ai-development"}'
```

### Scheduler Management

```bash
# Start the scheduler (every 2 hours)
curl -X POST http://localhost:3001/twitter/bot/scheduler/start \
  -H "Content-Type: application/json" \
  -d '{"cronExpression": "0 */2 * * *", "maxTweetsPerRun": 1}'

# Check scheduler status
curl -X GET http://localhost:3001/twitter/bot/scheduler/status

# Trigger manual run
curl -X POST http://localhost:3001/twitter/bot/scheduler/trigger
```

## Chain-of-Thought Prompting

The bot uses a sophisticated chain-of-thought prompting strategy for GPT-4 tweet generation:

1. **Content Analysis** - Identifies core updates and surprising insights
2. **Tone Selection** - Chooses appropriate tone (witty, serious, thought-provoking)
3. **Format Selection** - Picks engaging format (hot take, question, prediction)
4. **Hook Generation** - Creates attention-grabbing opening
5. **Developer Perspective** - Adds relevant technical insights
6. **Hashtag Selection** - Chooses 1-3 relevant hashtags
7. **URL Integration** - Ensures article link is included

## Content Quality Standards

### Article Validation
- Minimum 100 characters of content
- Maximum 3000 characters for cost optimization
- Valid URL format
- Successful content extraction

### Tweet Quality
- Maximum 280 characters
- Minimum 50 characters
- Contains original article URL
- 1-3 relevant hashtags
- Human-like, engaging tone

## Error Handling & Resilience

### Retry Logic
- **HTTP Requests**: 3 attempts with exponential backoff
- **OpenAI API**: 3 attempts with rate limit handling
- **Twitter API**: 3 attempts with Twitter-specific error handling

### Fallback Mechanisms
- Alternative article extraction methods
- Fallback tweet generation when AI fails
- Circuit breaker pattern for repeated failures

### Monitoring
- Comprehensive logging at all levels
- Token usage tracking for cost monitoring
- Error rate monitoring
- Performance metrics

## Cost Optimization

### Token Management
- Content truncation to stay under 1000 tokens
- Efficient prompt design
- Batch processing where possible

### Estimated Costs
- Article summarization: ~$0.001-0.002 per article
- Tweet generation: ~$0.03 per tweet
- Complete pipeline: ~$0.032 per posted tweet

## Testing

### Unit Tests
- ArticleFetcherService: Content extraction and validation
- TweetGeneratorService: Tweet quality and formatting
- Comprehensive mocking of external APIs

### Integration Tests
- End-to-end pipeline testing
- API endpoint validation
- Error scenario testing

## Deployment

### Prerequisites
- Node.js 18+
- NestJS CLI
- OpenAI API key
- Twitter API credentials
- Supabase database (optional)

### Installation

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys

# Run tests
npm test

# Start development server
npm run start:dev

# Build for production
npm run build
npm run start:prod
```

## Security Considerations

- API keys stored in environment variables
- No sensitive data in logs
- Rate limiting on API endpoints
- Input validation and sanitization
- Secure token refresh handling

## Future Enhancements

- Multiple tweet variations with A/B testing
- Engagement tracking and optimization
- Custom RSS feed management
- Advanced content filtering
- Multi-language support
- Image generation for tweets
- Thread creation for longer content
