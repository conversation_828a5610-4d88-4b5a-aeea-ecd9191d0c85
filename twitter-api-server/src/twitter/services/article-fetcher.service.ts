import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { ArticleData } from '../interfaces/twitter.interface';
import { RetryUtil } from '../utils/retry.util';

@Injectable()
export class ArticleFetcherService {
  private readonly logger = new Logger(ArticleFetcherService.name);
  private readonly MAX_CONTENT_LENGTH = 3000;
  private readonly REQUEST_TIMEOUT = 10000; // 10 seconds

  /**
   * Fetch and extract article content from a URL
   * Returns structured article data with title, content, and metadata
   */
  async fetchAndTrimArticle(url: string): Promise<ArticleData | null> {
    try {
      this.logger.log(`Fetching article from: ${url}`);

      // Validate URL
      if (!this.isValidUrl(url)) {
        this.logger.warn(`Invalid URL provided: ${url}`);
        return null;
      }

      // Fetch the HTML content with retry logic
      const response = await RetryUtil.withHttpRetry(
        () => axios.get(url, {
          timeout: this.REQUEST_TIMEOUT,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; TwitterBot/1.0; +https://twitter.com)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
          },
        }),
        `Fetching article from ${url}`
      );

      if (response.status !== 200) {
        this.logger.warn(`HTTP ${response.status} for URL: ${url}`);
        return null;
      }

      // Extract article content using @extractus/article-extractor
      const { extract } = await import('@extractus/article-extractor');
      const article = await extract(url);

      if (!article) {
        this.logger.warn(`Failed to extract article content from: ${url}`);
        return null;
      }

      // Clean and trim content
      const cleanContent = this.cleanAndTrimContent(article.content || '');
      
      if (!cleanContent || cleanContent.length < 100) {
        this.logger.warn(`Article content too short or empty for: ${url}`);
        return null;
      }

      const articleData: ArticleData = {
        title: article.title || 'Untitled Article',
        url: url,
        content: cleanContent,
        siteName: article.source || this.extractDomainFromUrl(url),
        publishedDate: article.published || undefined,
      };

      this.logger.log(`Successfully extracted article: "${articleData.title}" (${cleanContent.length} chars)`);
      return articleData;

    } catch (error) {
      this.logger.error(`Error fetching article from ${url}:`, error);
      
      // Try fallback method with direct HTML parsing
      return await this.fallbackFetch(url);
    }
  }

  /**
   * Fallback method using direct HTML fetching and basic parsing
   */
  private async fallbackFetch(url: string): Promise<ArticleData | null> {
    try {
      this.logger.log(`Attempting fallback fetch for: ${url}`);

      const response = await axios.get(url, {
        timeout: this.REQUEST_TIMEOUT,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; TwitterBot/1.0; +https://twitter.com)',
        },
      });

      const html = response.data;
      
      // Basic HTML parsing to extract title and content
      const title = this.extractTitleFromHtml(html) || 'Untitled Article';
      const content = this.extractContentFromHtml(html);

      if (!content || content.length < 100) {
        this.logger.warn(`Fallback extraction failed for: ${url}`);
        return null;
      }

      const articleData: ArticleData = {
        title,
        url,
        content: this.cleanAndTrimContent(content),
        siteName: this.extractDomainFromUrl(url),
      };

      this.logger.log(`Fallback extraction successful: "${title}"`);
      return articleData;

    } catch (error) {
      this.logger.error(`Fallback fetch failed for ${url}:`, error);
      return null;
    }
  }

  /**
   * Clean and trim content to specified length
   */
  private cleanAndTrimContent(content: string): string {
    if (!content) return '';

    // Remove excessive whitespace and newlines
    let cleaned = content
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, ' ')
      .trim();

    // Trim to maximum length while preserving word boundaries
    if (cleaned.length > this.MAX_CONTENT_LENGTH) {
      cleaned = cleaned.substring(0, this.MAX_CONTENT_LENGTH);
      const lastSpaceIndex = cleaned.lastIndexOf(' ');
      if (lastSpaceIndex > this.MAX_CONTENT_LENGTH * 0.8) {
        cleaned = cleaned.substring(0, lastSpaceIndex);
      }
      cleaned += '...';
    }

    return cleaned;
  }

  /**
   * Extract title from HTML using basic regex
   */
  private extractTitleFromHtml(html: string): string | null {
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    return titleMatch ? titleMatch[1].trim() : null;
  }

  /**
   * Extract content from HTML using basic parsing
   */
  private extractContentFromHtml(html: string): string {
    // Remove script and style tags
    let content = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
    content = content.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    
    // Extract text from common content containers
    const contentPatterns = [
      /<article[^>]*>([\s\S]*?)<\/article>/gi,
      /<main[^>]*>([\s\S]*?)<\/main>/gi,
      /<div[^>]*class="[^"]*content[^"]*"[^>]*>([\s\S]*?)<\/div>/gi,
      /<div[^>]*class="[^"]*post[^"]*"[^>]*>([\s\S]*?)<\/div>/gi,
    ];

    for (const pattern of contentPatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return this.stripHtmlTags(match[1]);
      }
    }

    // Fallback: extract from body
    const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
      return this.stripHtmlTags(bodyMatch[1]);
    }

    return this.stripHtmlTags(content);
  }

  /**
   * Strip HTML tags and decode entities
   */
  private stripHtmlTags(html: string): string {
    return html
      .replace(/<[^>]*>/g, ' ')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Extract domain from URL
   */
  private extractDomainFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch {
      return 'Unknown Source';
    }
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * Batch fetch multiple articles
   */
  async fetchMultipleArticles(urls: string[]): Promise<ArticleData[]> {
    this.logger.log(`Fetching ${urls.length} articles...`);
    
    const promises = urls.map(url => this.fetchAndTrimArticle(url));
    const results = await Promise.allSettled(promises);
    
    const articles = results
      .filter((result): result is PromiseFulfilledResult<ArticleData> => 
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);

    this.logger.log(`Successfully fetched ${articles.length} out of ${urls.length} articles`);
    return articles;
  }
}
