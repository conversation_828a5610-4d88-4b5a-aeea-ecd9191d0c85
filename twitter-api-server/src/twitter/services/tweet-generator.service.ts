import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import * as fs from 'fs';
import * as path from 'path';
import { ArticleData, ArticleSummary, GeneratedTweet } from '../interfaces/twitter.interface';
import { RetryUtil } from '../utils/retry.util';

@Injectable()
export class TweetGeneratorService {
  private readonly logger = new Logger(TweetGeneratorService.name);
  private readonly openai: OpenAI;
  private readonly MAX_TWEET_LENGTH = 280;
  private chainOfThoughtPrompt: string;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY not found in environment variables');
    }

    this.openai = new OpenAI({
      apiKey: apiKey,
    });

    // Load the chain-of-thought prompt
    this.loadChainOfThoughtPrompt();
  }

  /**
   * Load the chain-of-thought prompt from file
   */
  private loadChainOfThoughtPrompt(): void {
    try {
      const promptPath = path.join(__dirname, '../../prompts/chainOfThoughtPrompt.txt');
      this.chainOfThoughtPrompt = fs.readFileSync(promptPath, 'utf-8');
      this.logger.log('Chain-of-thought prompt loaded successfully');
    } catch (error) {
      this.logger.error('Failed to load chain-of-thought prompt:', error);
      // Fallback prompt
      this.chainOfThoughtPrompt = `You are a senior software engineer and Twitter creator. Create an engaging tweet about the given tech article that sounds human and adds developer insight. Include relevant hashtags and the article link. Maximum 280 characters.`;
    }
  }

  /**
   * Generate a tweet using GPT-4 with chain-of-thought prompting
   * Optimized for engagement (~$0.03 per tweet)
   */
  async generateTweetWithGPT4(
    articleData: ArticleData,
    summary: ArticleSummary
  ): Promise<GeneratedTweet | null> {
    try {
      this.logger.log(`Generating tweet for article: "${articleData.title}"`);

      // Build the input for GPT-4
      const input = this.buildTweetGenerationInput(articleData, summary);
      
      // Estimate token count for cost tracking
      const estimatedTokens = Math.ceil(input.length / 4);
      this.logger.log(`Estimated input tokens: ${estimatedTokens}`);

      // Call GPT-4 for tweet generation with retry logic
      const completion = await RetryUtil.withOpenAIRetry(
        () => this.openai.chat.completions.create({
          model: 'gpt-4', // Using GPT-4 for higher quality tweets
          messages: [
            {
              role: 'system',
              content: this.chainOfThoughtPrompt
            },
            {
              role: 'user',
              content: input
            }
          ],
          max_tokens: 150, // Limit response tokens since tweets are short
          temperature: 0.7, // Higher temperature for more creative, engaging content
          top_p: 0.9,
          presence_penalty: 0.1, // Slight penalty to avoid repetitive content
          frequency_penalty: 0.1,
        }),
        `Generating tweet for: ${articleData.title}`
      );

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        this.logger.error('No response from OpenAI GPT-4');
        return null;
      }

      // Parse and validate the generated tweet
      const generatedTweet = this.parseAndValidateTweet(response, articleData.url);
      
      if (!generatedTweet) {
        this.logger.error('Failed to generate valid tweet');
        return this.createFallbackTweet(articleData, summary);
      }

      // Log token usage for cost tracking
      const usage = completion.usage;
      if (usage) {
        this.logger.log(`Token usage - Prompt: ${usage.prompt_tokens}, Completion: ${usage.completion_tokens}, Total: ${usage.total_tokens}`);
        const estimatedCost = (usage.total_tokens / 1000) * 0.03; // Approximate cost for GPT-4
        this.logger.log(`Estimated cost: $${estimatedCost.toFixed(4)}`);
      }

      this.logger.log(`Successfully generated tweet: "${generatedTweet.content.substring(0, 50)}..."`);
      return generatedTweet;

    } catch (error) {
      this.logger.error(`Error generating tweet for "${articleData.title}":`, error);
      
      // Return fallback tweet for resilience
      return this.createFallbackTweet(articleData, summary);
    }
  }

  /**
   * Build the input for GPT-4 tweet generation
   */
  private buildTweetGenerationInput(articleData: ArticleData, summary: ArticleSummary): string {
    return `Article Information:
Title: ${articleData.title}
Source: ${articleData.siteName || 'Unknown'}
URL: ${articleData.url}

Article Summary:
Main Topic: ${summary.mainTopic}
Technical Focus: ${summary.technicalFocus}

Key Points:
${summary.keyPoints.map((point, index) => `${index + 1}. ${point}`).join('\n')}

Please create an engaging tweet following the chain-of-thought process outlined in your instructions. The tweet should be maximum 280 characters including hashtags and the article link.`;
  }

  /**
   * Parse and validate the generated tweet
   */
  private parseAndValidateTweet(response: string, originalUrl: string): GeneratedTweet | null {
    try {
      // Clean up the response
      let tweetContent = response.trim();
      
      // Remove any quotes or formatting that might be added
      tweetContent = tweetContent.replace(/^["']|["']$/g, '');
      
      // Extract hashtags
      const hashtagMatches = tweetContent.match(/#\w+/g) || [];
      const hashtags = hashtagMatches.map(tag => tag.toLowerCase());

      // Validate length
      if (tweetContent.length > this.MAX_TWEET_LENGTH) {
        this.logger.warn(`Generated tweet too long (${tweetContent.length} chars), attempting to trim`);
        tweetContent = this.trimTweetToLength(tweetContent, originalUrl);
      }

      // Ensure URL is included
      if (!tweetContent.includes('http')) {
        // Add URL if not present, but check length
        const tweetWithUrl = `${tweetContent} ${originalUrl}`;
        if (tweetWithUrl.length <= this.MAX_TWEET_LENGTH) {
          tweetContent = tweetWithUrl;
        } else {
          // Trim content to make room for URL
          const availableLength = this.MAX_TWEET_LENGTH - originalUrl.length - 1; // -1 for space
          tweetContent = tweetContent.substring(0, availableLength).trim() + ` ${originalUrl}`;
        }
      }

      // Final validation
      if (tweetContent.length > this.MAX_TWEET_LENGTH) {
        this.logger.error(`Tweet still too long after trimming: ${tweetContent.length} chars`);
        return null;
      }

      if (tweetContent.length < 50) {
        this.logger.error(`Tweet too short: ${tweetContent.length} chars`);
        return null;
      }

      return {
        content: tweetContent,
        characterCount: tweetContent.length,
        hashtags: hashtags,
        originalUrl: originalUrl,
        reasoning: 'Generated using GPT-4 with chain-of-thought prompting'
      };

    } catch (error) {
      this.logger.error('Error parsing generated tweet:', error);
      return null;
    }
  }

  /**
   * Trim tweet to fit within character limit
   */
  private trimTweetToLength(tweet: string, url: string): string {
    // Reserve space for URL if not already included
    const urlSpace = tweet.includes('http') ? 0 : url.length + 1;
    const maxContentLength = this.MAX_TWEET_LENGTH - urlSpace;

    if (tweet.length <= maxContentLength) {
      return tweet.includes('http') ? tweet : `${tweet} ${url}`;
    }

    // Find the last complete sentence or phrase
    let trimmed = tweet.substring(0, maxContentLength);
    
    // Try to trim at sentence boundary
    const lastSentenceEnd = Math.max(
      trimmed.lastIndexOf('.'),
      trimmed.lastIndexOf('!'),
      trimmed.lastIndexOf('?')
    );

    if (lastSentenceEnd > maxContentLength * 0.7) {
      trimmed = trimmed.substring(0, lastSentenceEnd + 1);
    } else {
      // Trim at word boundary
      const lastSpace = trimmed.lastIndexOf(' ');
      if (lastSpace > maxContentLength * 0.8) {
        trimmed = trimmed.substring(0, lastSpace);
      }
    }

    // Add URL if not present
    if (!trimmed.includes('http')) {
      trimmed = `${trimmed} ${url}`;
    }

    return trimmed;
  }

  /**
   * Create a fallback tweet when GPT-4 fails
   */
  private createFallbackTweet(articleData: ArticleData, summary: ArticleSummary): GeneratedTweet {
    // Create a simple but effective fallback tweet
    const mainPoint = summary.keyPoints[0] || summary.technicalFocus;
    const hashtag = this.selectRelevantHashtag(summary.mainTopic);
    
    let fallbackContent = `${summary.mainTopic}: ${mainPoint}`;
    
    // Add hashtag and URL
    const withHashtag = `${fallbackContent} ${hashtag}`;
    const withUrl = `${withHashtag} ${articleData.url}`;
    
    // Trim if necessary
    if (withUrl.length > this.MAX_TWEET_LENGTH) {
      const availableLength = this.MAX_TWEET_LENGTH - hashtag.length - articleData.url.length - 2; // -2 for spaces
      fallbackContent = fallbackContent.substring(0, availableLength).trim();
    }

    const finalTweet = `${fallbackContent} ${hashtag} ${articleData.url}`;

    return {
      content: finalTweet,
      characterCount: finalTweet.length,
      hashtags: [hashtag],
      originalUrl: articleData.url,
      reasoning: 'Fallback tweet generated due to GPT-4 failure'
    };
  }

  /**
   * Select relevant hashtag based on topic
   */
  private selectRelevantHashtag(topic: string): string {
    const topicLower = topic.toLowerCase();
    
    if (topicLower.includes('ai') || topicLower.includes('artificial intelligence')) return '#AI';
    if (topicLower.includes('machine learning') || topicLower.includes('ml')) return '#MachineLearning';
    if (topicLower.includes('programming') || topicLower.includes('coding')) return '#Programming';
    if (topicLower.includes('web') || topicLower.includes('frontend') || topicLower.includes('backend')) return '#WebDev';
    if (topicLower.includes('startup') || topicLower.includes('business')) return '#Startup';
    if (topicLower.includes('open source') || topicLower.includes('github')) return '#OpenSource';
    if (topicLower.includes('data') || topicLower.includes('analytics')) return '#DataScience';
    
    return '#TechNews'; // Default fallback
  }

  /**
   * Generate multiple tweet variations
   */
  async generateTweetVariations(
    articleData: ArticleData,
    summary: ArticleSummary,
    count: number = 3
  ): Promise<GeneratedTweet[]> {
    this.logger.log(`Generating ${count} tweet variations for: "${articleData.title}"`);
    
    const tweets: GeneratedTweet[] = [];
    
    for (let i = 0; i < count; i++) {
      const tweet = await this.generateTweetWithGPT4(articleData, summary);
      if (tweet) {
        tweets.push(tweet);
      }
      
      // Small delay between requests
      if (i < count - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    this.logger.log(`Generated ${tweets.length} tweet variations`);
    return tweets;
  }

  /**
   * Select the best tweet from multiple variations
   */
  selectBestTweet(tweets: GeneratedTweet[]): GeneratedTweet | null {
    if (tweets.length === 0) return null;
    if (tweets.length === 1) return tweets[0];

    // Score tweets based on various factors
    const scoredTweets = tweets.map(tweet => ({
      tweet,
      score: this.scoreTweet(tweet)
    }));

    // Sort by score and return the best one
    scoredTweets.sort((a, b) => b.score - a.score);
    
    this.logger.log(`Selected best tweet with score: ${scoredTweets[0].score}`);
    return scoredTweets[0].tweet;
  }

  /**
   * Score a tweet based on engagement factors
   */
  private scoreTweet(tweet: GeneratedTweet): number {
    let score = 0;

    // Length optimization (tweets around 100-150 chars tend to perform well)
    const idealLength = 120;
    const lengthDiff = Math.abs(tweet.characterCount - idealLength);
    score += Math.max(0, 50 - lengthDiff); // Max 50 points for optimal length

    // Hashtag optimization (1-3 hashtags is optimal)
    const hashtagCount = tweet.hashtags.length;
    if (hashtagCount >= 1 && hashtagCount <= 3) {
      score += 20;
    } else if (hashtagCount > 3) {
      score -= 10; // Penalty for too many hashtags
    }

    // Content quality indicators
    const content = tweet.content.toLowerCase();
    
    // Engagement words
    const engagementWords = ['new', 'update', 'breaking', 'just', 'now', 'finally', 'major', 'huge', 'game-changer'];
    const engagementCount = engagementWords.filter(word => content.includes(word)).length;
    score += engagementCount * 5;

    // Question marks (encourage engagement)
    const questionCount = (tweet.content.match(/\?/g) || []).length;
    score += questionCount * 10;

    // Avoid generic phrases
    const genericPhrases = ['check out', 'read more', 'click here', 'learn about'];
    const genericCount = genericPhrases.filter(phrase => content.includes(phrase)).length;
    score -= genericCount * 15;

    return score;
  }
}
