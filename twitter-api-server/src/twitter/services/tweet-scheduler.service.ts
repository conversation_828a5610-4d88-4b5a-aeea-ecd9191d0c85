import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as cron from 'node-cron';
import { TwitterService } from '../twitter.service';
import { ArticleFetcherService } from './article-fetcher.service';
import { ArticleSummarizerService } from './article-summarizer.service';
import { TweetGeneratorService } from './tweet-generator.service';
import { DatabaseService } from '../../database/database.service';
import { BotScheduleConfig } from '../interfaces/twitter.interface';

@Injectable()
export class TweetSchedulerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(TweetSchedulerService.name);
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();
  private isRunning = false;
  private postedUrls = new Set<string>();

  // Default configuration
  private defaultConfig: BotScheduleConfig = {
    enabled: false,
    cronExpression: '0 */2 * * *', // Every 2 hours
    feedUrls: [
      'https://techcrunch.com/feed/',
      'https://www.theverge.com/rss/index.xml',
      'https://news.ycombinator.com/rss',
      'https://feeds.feedburner.com/oreilly/radar',
    ],
    maxTweetsPerRun: 1,
  };

  constructor(
    private configService: ConfigService,
    private twitterService: TwitterService,
    private articleFetcher: ArticleFetcherService,
    private articleSummarizer: ArticleSummarizerService,
    private tweetGenerator: TweetGeneratorService,
    private databaseService: DatabaseService,
  ) {}

  async onModuleInit() {
    this.logger.log('Tweet Scheduler Service initialized');
    
    // Load previously posted URLs from database
    await this.loadPostedUrls();
    
    // Start default scheduler if enabled
    const isEnabled = this.configService.get<boolean>('TWITTER_BOT_ENABLED', false);
    if (isEnabled) {
      await this.startScheduler();
    }
  }

  async onModuleDestroy() {
    this.logger.log('Stopping all scheduled tasks...');
    this.stopAllSchedulers();
  }

  /**
   * Start the main tweet scheduler
   */
  async startScheduler(config?: Partial<BotScheduleConfig>): Promise<void> {
    try {
      const finalConfig = { ...this.defaultConfig, ...config };
      
      if (!finalConfig.enabled) {
        this.logger.log('Tweet scheduler is disabled');
        return;
      }

      // Stop existing scheduler if running
      this.stopScheduler('main');

      this.logger.log(`Starting tweet scheduler with cron: ${finalConfig.cronExpression}`);
      
      const task = cron.schedule(
        finalConfig.cronExpression,
        async () => {
          if (this.isRunning) {
            this.logger.warn('Previous scheduler run still in progress, skipping...');
            return;
          }

          await this.runScheduledTweetGeneration(finalConfig);
        },
        {
          scheduled: true,
          timezone: 'UTC',
        }
      );

      this.scheduledTasks.set('main', task);
      this.logger.log('Tweet scheduler started successfully');

    } catch (error) {
      this.logger.error('Error starting tweet scheduler:', error);
      throw error;
    }
  }

  /**
   * Stop a specific scheduler
   */
  stopScheduler(name: string): void {
    const task = this.scheduledTasks.get(name);
    if (task) {
      task.stop();
      task.destroy();
      this.scheduledTasks.delete(name);
      this.logger.log(`Stopped scheduler: ${name}`);
    }
  }

  /**
   * Stop all schedulers
   */
  stopAllSchedulers(): void {
    for (const [name, task] of this.scheduledTasks) {
      task.stop();
      task.destroy();
      this.logger.log(`Stopped scheduler: ${name}`);
    }
    this.scheduledTasks.clear();
  }

  /**
   * Run the complete tweet generation pipeline
   */
  async runScheduledTweetGeneration(config: BotScheduleConfig): Promise<void> {
    this.isRunning = true;
    const startTime = Date.now();

    try {
      this.logger.log('🚀 Starting scheduled tweet generation...');

      // Step 1: Fetch articles from RSS feeds
      const articles = await this.fetchArticlesFromFeeds(config.feedUrls);
      
      if (articles.length === 0) {
        this.logger.warn('No new articles found to process');
        return;
      }

      this.logger.log(`📰 Found ${articles.length} new articles to process`);

      // Step 2: Process articles and generate tweets
      let tweetsPosted = 0;
      const maxTweets = config.maxTweetsPerRun;

      for (const article of articles) {
        if (tweetsPosted >= maxTweets) {
          this.logger.log(`Reached maximum tweets per run (${maxTweets})`);
          break;
        }

        try {
          // Step 3: Summarize article with GPT-3.5
          this.logger.log(`📝 Summarizing: "${article.title}"`);
          const summary = await this.articleSummarizer.summarizeWithGPT3(article);
          
          if (!summary) {
            this.logger.warn(`Failed to summarize article: "${article.title}"`);
            continue;
          }

          // Step 4: Generate tweet with GPT-4
          this.logger.log(`🐦 Generating tweet for: "${article.title}"`);
          const generatedTweet = await this.tweetGenerator.generateTweetWithGPT4(article, summary);
          
          if (!generatedTweet) {
            this.logger.warn(`Failed to generate tweet for: "${article.title}"`);
            continue;
          }

          // Step 5: Post tweet to Twitter
          this.logger.log(`📤 Posting tweet: "${generatedTweet.content.substring(0, 50)}..."`);
          const postedTweet = await this.twitterService.postTweet(generatedTweet.content);
          
          if (postedTweet) {
            // Mark URL as posted
            this.postedUrls.add(article.url);
            await this.savePostedUrl(article.url);
            
            // Save to database with additional metadata
            await this.databaseService.addPostedTweet({
              tweet_id: postedTweet.id,
              content: generatedTweet.content,
              original_url: article.url,
              original_title: article.title,
              impressions: 0,
              retweets: 0,
              likes: 0,
              replies: 0,
              posted_at: new Date().toISOString(),
            });

            tweetsPosted++;
            this.logger.log(`✅ Successfully posted tweet ${tweetsPosted}/${maxTweets}`);

            // Add delay between tweets to avoid rate limits
            if (tweetsPosted < maxTweets && tweetsPosted < articles.length) {
              await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay
            }
          }

        } catch (error) {
          this.logger.error(`Error processing article "${article.title}":`, error);
          continue; // Continue with next article
        }
      }

      const duration = Date.now() - startTime;
      this.logger.log(`🎉 Scheduled run completed: ${tweetsPosted} tweets posted in ${duration}ms`);

    } catch (error) {
      this.logger.error('Error in scheduled tweet generation:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Fetch articles from RSS feeds
   */
  private async fetchArticlesFromFeeds(feedUrls: string[]) {
    const Parser = require('rss-parser');
    const parser = new Parser();
    const allArticles = [];

    for (const feedUrl of feedUrls) {
      try {
        this.logger.log(`Fetching RSS feed: ${feedUrl}`);
        const feed = await parser.parseURL(feedUrl);
        
        // Get recent articles that haven't been posted
        const recentArticles = feed.items
          .slice(0, 10) // Get latest 10 items
          .filter(item => !this.postedUrls.has(item.link))
          .map(item => ({
            title: item.title,
            url: item.link,
            content: item.contentSnippet || item.content || item.summary || '',
            siteName: feed.title,
            publishedDate: item.pubDate,
          }));

        // Fetch full article content
        for (const article of recentArticles) {
          const fullArticle = await this.articleFetcher.fetchAndTrimArticle(article.url);
          if (fullArticle) {
            allArticles.push(fullArticle);
          }
        }

      } catch (error) {
        this.logger.error(`Error fetching RSS feed ${feedUrl}:`, error);
        continue;
      }
    }

    // Sort by recency and remove duplicates
    const uniqueArticles = allArticles.filter((article, index, self) => 
      index === self.findIndex(a => a.url === article.url)
    );

    return uniqueArticles.slice(0, 20); // Limit to 20 most recent articles
  }

  /**
   * Load previously posted URLs from database
   */
  private async loadPostedUrls(): Promise<void> {
    try {
      // This would typically query the database for posted tweets
      // For now, we'll implement a simple in-memory solution
      this.logger.log('Loading posted URLs from database...');
      
      // TODO: Implement database query to get posted URLs
      // const postedTweets = await this.databaseService.getPostedTweets();
      // postedTweets.forEach(tweet => {
      //   if (tweet.original_url) {
      //     this.postedUrls.add(tweet.original_url);
      //   }
      // });

      this.logger.log(`Loaded ${this.postedUrls.size} previously posted URLs`);
    } catch (error) {
      this.logger.error('Error loading posted URLs:', error);
    }
  }

  /**
   * Save posted URL to prevent duplicates
   */
  private async savePostedUrl(url: string): Promise<void> {
    try {
      // TODO: Implement database storage for posted URLs
      // This could be a simple table or part of the posted_tweets table
      this.logger.debug(`Saved posted URL: ${url}`);
    } catch (error) {
      this.logger.error('Error saving posted URL:', error);
    }
  }

  /**
   * Manual trigger for tweet generation (for testing)
   */
  async triggerManualRun(config?: Partial<BotScheduleConfig>): Promise<void> {
    const finalConfig = { ...this.defaultConfig, ...config, enabled: true };
    this.logger.log('🔧 Manual tweet generation triggered');
    await this.runScheduledTweetGeneration(finalConfig);
  }

  /**
   * Get scheduler status
   */
  getSchedulerStatus(): {
    isRunning: boolean;
    activeSchedulers: string[];
    postedUrlsCount: number;
  } {
    return {
      isRunning: this.isRunning,
      activeSchedulers: Array.from(this.scheduledTasks.keys()),
      postedUrlsCount: this.postedUrls.size,
    };
  }

  /**
   * Update scheduler configuration
   */
  async updateSchedulerConfig(config: Partial<BotScheduleConfig>): Promise<void> {
    this.logger.log('Updating scheduler configuration...');
    
    // Stop current scheduler
    this.stopScheduler('main');
    
    // Start with new configuration
    await this.startScheduler(config);
    
    this.logger.log('Scheduler configuration updated');
  }
}
