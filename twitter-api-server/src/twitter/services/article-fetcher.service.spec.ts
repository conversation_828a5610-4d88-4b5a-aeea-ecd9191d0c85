import { Test, TestingModule } from '@nestjs/testing';
import { ArticleFetcherService } from './article-fetcher.service';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock @extractus/article-extractor
jest.mock('@extractus/article-extractor', () => ({
  extract: jest.fn(),
}));

import { extract } from '@extractus/article-extractor';
const mockedExtract = extract as jest.MockedFunction<typeof extract>;

describe('ArticleFetcherService', () => {
  let service: ArticleFetcherService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ArticleFetcherService],
    }).compile();

    service = module.get<ArticleFetcherService>(ArticleFetcherService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('fetchAndTrimArticle', () => {
    const mockUrl = 'https://example.com/article';
    const mockHtmlResponse = {
      status: 200,
      data: '<html><body><h1>Test Article</h1><p>Test content</p></body></html>',
    };

    const mockExtractedArticle = {
      title: 'Test Article',
      content: 'This is a test article with some content that should be extracted properly.',
      source: 'example.com',
      published: '2023-01-01T00:00:00Z',
    };

    it('should successfully fetch and extract article', async () => {
      mockedAxios.get.mockResolvedValue(mockHtmlResponse);
      mockedExtract.mockResolvedValue(mockExtractedArticle);

      const result = await service.fetchAndTrimArticle(mockUrl);

      expect(result).toEqual({
        title: 'Test Article',
        url: mockUrl,
        content: 'This is a test article with some content that should be extracted properly.',
        siteName: 'example.com',
        publishedDate: '2023-01-01T00:00:00Z',
      });

      expect(mockedAxios.get).toHaveBeenCalledWith(mockUrl, expect.objectContaining({
        timeout: 10000,
        headers: expect.objectContaining({
          'User-Agent': 'Mozilla/5.0 (compatible; TwitterBot/1.0; +https://twitter.com)',
        }),
      }));

      expect(mockedExtract).toHaveBeenCalledWith(mockUrl, expect.objectContaining({
        headers: expect.objectContaining({
          'User-Agent': 'Mozilla/5.0 (compatible; TwitterBot/1.0; +https://twitter.com)',
        }),
      }));
    });

    it('should return null for invalid URL', async () => {
      const result = await service.fetchAndTrimArticle('invalid-url');
      expect(result).toBeNull();
    });

    it('should return null when HTTP request fails', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      const result = await service.fetchAndTrimArticle(mockUrl);
      expect(result).toBeNull();
    });

    it('should return null when article extraction fails', async () => {
      mockedAxios.get.mockResolvedValue(mockHtmlResponse);
      mockedExtract.mockResolvedValue(null);

      const result = await service.fetchAndTrimArticle(mockUrl);
      expect(result).toBeNull();
    });

    it('should trim content to maximum length', async () => {
      const longContent = 'A'.repeat(5000); // Content longer than 3000 chars
      const mockLongArticle = {
        ...mockExtractedArticle,
        content: longContent,
      };

      mockedAxios.get.mockResolvedValue(mockHtmlResponse);
      mockedExtract.mockResolvedValue(mockLongArticle);

      const result = await service.fetchAndTrimArticle(mockUrl);

      expect(result).not.toBeNull();
      expect(result!.content.length).toBeLessThanOrEqual(3003); // 3000 + '...'
      expect(result!.content).toMatch(/\.\.\.$/); // Should end with '...'
    });

    it('should return null for content that is too short', async () => {
      const shortContent = 'Short'; // Less than 100 characters
      const mockShortArticle = {
        ...mockExtractedArticle,
        content: shortContent,
      };

      mockedAxios.get.mockResolvedValue(mockHtmlResponse);
      mockedExtract.mockResolvedValue(mockShortArticle);

      const result = await service.fetchAndTrimArticle(mockUrl);
      expect(result).toBeNull();
    });

    it('should handle missing title gracefully', async () => {
      const mockArticleNoTitle = {
        ...mockExtractedArticle,
        title: null,
      };

      mockedAxios.get.mockResolvedValue(mockHtmlResponse);
      mockedExtract.mockResolvedValue(mockArticleNoTitle);

      const result = await service.fetchAndTrimArticle(mockUrl);

      expect(result).not.toBeNull();
      expect(result!.title).toBe('Untitled Article');
    });

    it('should extract domain from URL when source is missing', async () => {
      const mockArticleNoSource = {
        ...mockExtractedArticle,
        source: null,
      };

      mockedAxios.get.mockResolvedValue(mockHtmlResponse);
      mockedExtract.mockResolvedValue(mockArticleNoSource);

      const result = await service.fetchAndTrimArticle(mockUrl);

      expect(result).not.toBeNull();
      expect(result!.siteName).toBe('example.com');
    });
  });

  describe('fetchMultipleArticles', () => {
    it('should fetch multiple articles successfully', async () => {
      const urls = [
        'https://example1.com/article',
        'https://example2.com/article',
      ];

      const mockArticle1 = {
        title: 'Article 1',
        content: 'Content 1 with enough length to pass validation checks for minimum content requirements.',
        source: 'example1.com',
      };

      const mockArticle2 = {
        title: 'Article 2',
        content: 'Content 2 with enough length to pass validation checks for minimum content requirements.',
        source: 'example2.com',
      };

      mockedAxios.get
        .mockResolvedValueOnce({ status: 200, data: '<html>Article 1</html>' })
        .mockResolvedValueOnce({ status: 200, data: '<html>Article 2</html>' });

      mockedExtract
        .mockResolvedValueOnce(mockArticle1)
        .mockResolvedValueOnce(mockArticle2);

      const results = await service.fetchMultipleArticles(urls);

      expect(results).toHaveLength(2);
      expect(results[0].title).toBe('Article 1');
      expect(results[1].title).toBe('Article 2');
    });

    it('should handle partial failures gracefully', async () => {
      const urls = [
        'https://example1.com/article',
        'https://example2.com/article',
      ];

      const mockArticle1 = {
        title: 'Article 1',
        content: 'Content 1 with enough length to pass validation checks for minimum content requirements.',
        source: 'example1.com',
      };

      mockedAxios.get
        .mockResolvedValueOnce({ status: 200, data: '<html>Article 1</html>' })
        .mockRejectedValueOnce(new Error('Network error'));

      mockedExtract.mockResolvedValueOnce(mockArticle1);

      const results = await service.fetchMultipleArticles(urls);

      expect(results).toHaveLength(1);
      expect(results[0].title).toBe('Article 1');
    });

    it('should return empty array when all fetches fail', async () => {
      const urls = ['https://example.com/article'];

      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      const results = await service.fetchMultipleArticles(urls);

      expect(results).toHaveLength(0);
    });
  });

  describe('private methods', () => {
    it('should validate URLs correctly', () => {
      // Access private method through any casting for testing
      const serviceAny = service as any;

      expect(serviceAny.isValidUrl('https://example.com')).toBe(true);
      expect(serviceAny.isValidUrl('http://example.com')).toBe(true);
      expect(serviceAny.isValidUrl('ftp://example.com')).toBe(false);
      expect(serviceAny.isValidUrl('invalid-url')).toBe(false);
      expect(serviceAny.isValidUrl('')).toBe(false);
    });

    it('should extract domain from URL correctly', () => {
      const serviceAny = service as any;

      expect(serviceAny.extractDomainFromUrl('https://www.example.com/path')).toBe('example.com');
      expect(serviceAny.extractDomainFromUrl('https://subdomain.example.com')).toBe('subdomain.example.com');
      expect(serviceAny.extractDomainFromUrl('invalid-url')).toBe('Unknown Source');
    });

    it('should clean and trim content correctly', () => {
      const serviceAny = service as any;

      const messyContent = '  This   is\n\n  messy    content  \n  ';
      const cleaned = serviceAny.cleanAndTrimContent(messyContent);
      expect(cleaned).toBe('This is messy content');

      const longContent = 'A'.repeat(5000);
      const trimmed = serviceAny.cleanAndTrimContent(longContent);
      expect(trimmed.length).toBeLessThanOrEqual(3003);
      expect(trimmed).toMatch(/\.\.\.$/);
    });
  });
});
