import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { ArticleData, ArticleSummary } from '../interfaces/twitter.interface';
import { RetryUtil } from '../utils/retry.util';

@Injectable()
export class ArticleSummarizerService {
  private readonly logger = new Logger(ArticleSummarizerService.name);
  private readonly openai: OpenAI;
  private readonly MAX_TOKENS = 800; // Keep content under 1000 tokens for cost optimization

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      this.logger.warn('OPENAI_API_KEY not found in environment variables. OpenAI features will be disabled.');
      this.openai = null as any; // Will be handled in methods
    } else {
      this.openai = new OpenAI({
        apiKey: apiKey,
      });
    }
  }

  /**
   * Summarize article content using GPT-3.5 Turbo
   * Optimized for cost (~$0.001-0.002 per call)
   */
  async summarizeWithGPT3(articleData: ArticleData): Promise<ArticleSummary | null> {
    try {
      if (!this.openai) {
        this.logger.warn('OpenAI not configured, returning fallback summary');
        return this.createFallbackSummary(articleData);
      }

      this.logger.log(`Summarizing article: "${articleData.title}"`);

      // Prepare the prompt for GPT-3.5
      const prompt = this.buildSummarizationPrompt(articleData);
      
      // Estimate token count (rough approximation: 1 token ≈ 4 characters)
      const estimatedTokens = Math.ceil(prompt.length / 4);
      this.logger.log(`Estimated tokens: ${estimatedTokens}`);

      if (estimatedTokens > this.MAX_TOKENS) {
        this.logger.warn(`Content too long (${estimatedTokens} tokens), truncating...`);
        // Truncate content while preserving structure
        const truncatedContent = this.truncateContent(articleData.content, this.MAX_TOKENS * 3); // ~3 chars per token
        const truncatedArticle = { ...articleData, content: truncatedContent };
        return await this.summarizeWithGPT3(truncatedArticle);
      }

      // Call GPT-3.5 Turbo for summarization with retry logic
      const completion = await RetryUtil.withOpenAIRetry(
        () => this.openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a technical content analyst specializing in developer and tech industry news. Your job is to extract key insights from articles and present them in a structured format.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 300, // Limit response tokens for cost control
          temperature: 0.3, // Lower temperature for more consistent, factual summaries
          top_p: 0.9,
        }),
        `Summarizing article: ${articleData.title}`
      );

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        this.logger.error('No response from OpenAI GPT-3.5');
        return null;
      }

      // Parse the structured response
      const summary = this.parseGPTResponse(response);
      
      if (!summary) {
        this.logger.error('Failed to parse GPT-3.5 response');
        return null;
      }

      // Log token usage for cost tracking
      const usage = completion.usage;
      if (usage) {
        this.logger.log(`Token usage - Prompt: ${usage.prompt_tokens}, Completion: ${usage.completion_tokens}, Total: ${usage.total_tokens}`);
        const estimatedCost = (usage.total_tokens / 1000) * 0.002; // Approximate cost for GPT-3.5-turbo
        this.logger.log(`Estimated cost: $${estimatedCost.toFixed(4)}`);
      }

      this.logger.log(`Successfully summarized article: "${articleData.title}"`);
      return summary;

    } catch (error) {
      this.logger.error(`Error summarizing article "${articleData.title}":`, error);
      
      // Return fallback summary for resilience
      return this.createFallbackSummary(articleData);
    }
  }

  /**
   * Build the summarization prompt optimized for developer content
   */
  private buildSummarizationPrompt(articleData: ArticleData): string {
    return `Analyze this tech article and provide a structured summary:

Title: ${articleData.title}
Source: ${articleData.siteName || 'Unknown'}
URL: ${articleData.url}

Content:
${articleData.content}

Please provide your analysis in this exact JSON format:
{
  "keyPoints": [
    "First key insight or update",
    "Second key insight or update", 
    "Third key insight or update",
    "Fourth key insight or update (if applicable)",
    "Fifth key insight or update (if applicable)"
  ],
  "mainTopic": "Primary subject/technology/company discussed",
  "technicalFocus": "Main technical implication or significance for developers"
}

Focus on:
- Technical updates, product launches, or strategic changes
- Implications for developers, engineers, or tech professionals
- Business/industry impact
- New technologies, tools, or methodologies
- Keep each key point concise (1-2 sentences max)
- Prioritize the most newsworthy and actionable insights`;
  }

  /**
   * Parse GPT response into structured summary
   */
  private parseGPTResponse(response: string): ArticleSummary | null {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        this.logger.warn('No JSON found in GPT response, attempting fallback parsing');
        return this.fallbackParseResponse(response);
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Validate the structure
      if (!parsed.keyPoints || !Array.isArray(parsed.keyPoints) || parsed.keyPoints.length === 0) {
        this.logger.warn('Invalid keyPoints in GPT response');
        return null;
      }

      return {
        keyPoints: parsed.keyPoints.slice(0, 5), // Limit to 5 key points
        mainTopic: parsed.mainTopic || 'Technology Update',
        technicalFocus: parsed.technicalFocus || 'General tech development'
      };

    } catch (error) {
      this.logger.error('Error parsing GPT response:', error);
      return this.fallbackParseResponse(response);
    }
  }

  /**
   * Fallback parsing when JSON parsing fails
   */
  private fallbackParseResponse(response: string): ArticleSummary | null {
    try {
      // Extract bullet points or numbered lists
      const lines = response.split('\n').filter(line => line.trim());
      const keyPoints: string[] = [];
      let mainTopic = 'Technology Update';
      let technicalFocus = 'General tech development';

      for (const line of lines) {
        const trimmed = line.trim();
        
        // Look for bullet points or numbered items
        if (trimmed.match(/^[-•*]\s+/) || trimmed.match(/^\d+\.\s+/)) {
          const point = trimmed.replace(/^[-•*]\s+/, '').replace(/^\d+\.\s+/, '');
          if (point.length > 10 && keyPoints.length < 5) {
            keyPoints.push(point);
          }
        }
        
        // Look for topic indicators
        if (trimmed.toLowerCase().includes('topic:') || trimmed.toLowerCase().includes('subject:')) {
          mainTopic = trimmed.split(':')[1]?.trim() || mainTopic;
        }
        
        if (trimmed.toLowerCase().includes('focus:') || trimmed.toLowerCase().includes('implication:')) {
          technicalFocus = trimmed.split(':')[1]?.trim() || technicalFocus;
        }
      }

      if (keyPoints.length === 0) {
        return null;
      }

      return {
        keyPoints,
        mainTopic,
        technicalFocus
      };

    } catch (error) {
      this.logger.error('Fallback parsing failed:', error);
      return null;
    }
  }

  /**
   * Create a basic fallback summary when AI fails
   */
  private createFallbackSummary(articleData: ArticleData): ArticleSummary {
    // Extract first few sentences as key points
    const sentences = articleData.content.split(/[.!?]+/).filter(s => s.trim().length > 20);
    const keyPoints = sentences.slice(0, 3).map(s => s.trim() + '.');

    return {
      keyPoints: keyPoints.length > 0 ? keyPoints : ['Article discusses recent developments in technology.'],
      mainTopic: articleData.title,
      technicalFocus: `Updates from ${articleData.siteName || 'tech industry'}`
    };
  }

  /**
   * Truncate content to fit within token limits
   */
  private truncateContent(content: string, maxChars: number): string {
    if (content.length <= maxChars) {
      return content;
    }

    // Try to truncate at sentence boundary
    const truncated = content.substring(0, maxChars);
    const lastSentenceEnd = Math.max(
      truncated.lastIndexOf('.'),
      truncated.lastIndexOf('!'),
      truncated.lastIndexOf('?')
    );

    if (lastSentenceEnd > maxChars * 0.7) {
      return truncated.substring(0, lastSentenceEnd + 1);
    }

    // Fallback to word boundary
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > maxChars * 0.8) {
      return truncated.substring(0, lastSpace) + '...';
    }

    return truncated + '...';
  }

  /**
   * Batch summarize multiple articles
   */
  async summarizeMultipleArticles(articles: ArticleData[]): Promise<ArticleSummary[]> {
    this.logger.log(`Summarizing ${articles.length} articles...`);
    
    const summaries: ArticleSummary[] = [];
    
    // Process articles sequentially to avoid rate limits
    for (const article of articles) {
      const summary = await this.summarizeWithGPT3(article);
      if (summary) {
        summaries.push(summary);
      }
      
      // Small delay to respect rate limits
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.logger.log(`Successfully summarized ${summaries.length} out of ${articles.length} articles`);
    return summaries;
  }
}
