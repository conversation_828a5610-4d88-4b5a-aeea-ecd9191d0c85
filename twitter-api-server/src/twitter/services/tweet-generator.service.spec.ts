import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { TweetGeneratorService } from './tweet-generator.service';
import { ArticleData, ArticleSummary } from '../interfaces/twitter.interface';

// Mock OpenAI
const mockOpenAI = {
  chat: {
    completions: {
      create: jest.fn(),
    },
  },
};

jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => mockOpenAI);
});

// Mock fs
jest.mock('fs', () => ({
  readFileSync: jest.fn(),
}));

import * as fs from 'fs';
const mockedFs = fs as jest.Mocked<typeof fs>;

describe('TweetGeneratorService', () => {
  let service: TweetGeneratorService;
  let configService: ConfigService;

  const mockArticleData: ArticleData = {
    title: 'Test Article About AI Development',
    url: 'https://example.com/ai-article',
    content: 'This is a comprehensive article about artificial intelligence development and its implications for developers.',
    siteName: 'TechNews',
    publishedDate: '2023-01-01T00:00:00Z',
  };

  const mockSummary: ArticleSummary = {
    keyPoints: [
      'AI development is accelerating rapidly',
      'New frameworks are making AI more accessible',
      'Developers need to adapt to AI-first workflows',
    ],
    mainTopic: 'AI Development',
    technicalFocus: 'Developer tools and frameworks',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TweetGeneratorService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-api-key'),
          },
        },
      ],
    }).compile();

    service = module.get<TweetGeneratorService>(TweetGeneratorService);
    configService = module.get<ConfigService>(ConfigService);

    // Mock the prompt file reading
    mockedFs.readFileSync.mockReturnValue('Mock chain-of-thought prompt content');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateTweetWithGPT4', () => {
    const mockGPTResponse = {
      choices: [
        {
          message: {
            content: 'AI development is accelerating rapidly. New frameworks making it accessible to all devs. Time to embrace the AI-first future! #AI #DevLife https://example.com/ai-article',
          },
        },
      ],
      usage: {
        prompt_tokens: 150,
        completion_tokens: 50,
        total_tokens: 200,
      },
    };

    it('should generate a valid tweet', async () => {
      mockOpenAI.chat.completions.create.mockResolvedValue(mockGPTResponse);

      const result = await service.generateTweetWithGPT4(mockArticleData, mockSummary);

      expect(result).not.toBeNull();
      expect(result!.content).toContain('AI development');
      expect(result!.content).toContain('#AI');
      expect(result!.content).toContain('https://example.com/ai-article');
      expect(result!.characterCount).toBeLessThanOrEqual(280);
      expect(result!.hashtags).toContain('#ai');
      expect(result!.originalUrl).toBe(mockArticleData.url);
    });

    it('should handle OpenAI API failure gracefully', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'));

      const result = await service.generateTweetWithGPT4(mockArticleData, mockSummary);

      expect(result).not.toBeNull();
      expect(result!.content).toContain(mockSummary.mainTopic);
      expect(result!.content).toContain(mockArticleData.url);
      expect(result!.reasoning).toContain('fallback');
    });

    it('should handle empty response from OpenAI', async () => {
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [],
        usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 },
      });

      const result = await service.generateTweetWithGPT4(mockArticleData, mockSummary);

      expect(result).not.toBeNull();
      expect(result!.reasoning).toContain('fallback');
    });

    it('should trim tweet if too long', async () => {
      const longTweetResponse = {
        choices: [
          {
            message: {
              content: 'This is a very long tweet that exceeds the 280 character limit and should be trimmed automatically by the service to ensure it fits within Twitter\'s character constraints while maintaining readability and including the URL https://example.com/ai-article',
            },
          },
        ],
        usage: { prompt_tokens: 150, completion_tokens: 50, total_tokens: 200 },
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(longTweetResponse);

      const result = await service.generateTweetWithGPT4(mockArticleData, mockSummary);

      expect(result).not.toBeNull();
      expect(result!.characterCount).toBeLessThanOrEqual(280);
      expect(result!.content).toContain(mockArticleData.url);
    });

    it('should add URL if missing from generated tweet', async () => {
      const tweetWithoutUrl = {
        choices: [
          {
            message: {
              content: 'AI development is accelerating rapidly. New frameworks making it accessible! #AI #DevLife',
            },
          },
        ],
        usage: { prompt_tokens: 150, completion_tokens: 50, total_tokens: 200 },
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(tweetWithoutUrl);

      const result = await service.generateTweetWithGPT4(mockArticleData, mockSummary);

      expect(result).not.toBeNull();
      expect(result!.content).toContain(mockArticleData.url);
    });

    it('should log token usage for cost tracking', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      mockOpenAI.chat.completions.create.mockResolvedValue(mockGPTResponse);

      await service.generateTweetWithGPT4(mockArticleData, mockSummary);

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          model: 'gpt-4',
          max_tokens: 150,
          temperature: 0.7,
        })
      );

      consoleSpy.mockRestore();
    });
  });

  describe('generateTweetVariations', () => {
    it('should generate multiple tweet variations', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Test tweet #AI https://example.com/ai-article' } }],
        usage: { prompt_tokens: 150, completion_tokens: 50, total_tokens: 200 },
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const variations = await service.generateTweetVariations(mockArticleData, mockSummary, 2);

      expect(variations).toHaveLength(2);
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(2);
    });

    it('should handle partial failures in variations', async () => {
      mockOpenAI.chat.completions.create
        .mockResolvedValueOnce({
          choices: [{ message: { content: 'First tweet #AI https://example.com/ai-article' } }],
          usage: { prompt_tokens: 150, completion_tokens: 50, total_tokens: 200 },
        })
        .mockRejectedValueOnce(new Error('API Error'));

      const variations = await service.generateTweetVariations(mockArticleData, mockSummary, 2);

      expect(variations).toHaveLength(1);
    });
  });

  describe('selectBestTweet', () => {
    const mockTweets = [
      {
        content: 'Basic tweet about AI #AI https://example.com',
        characterCount: 45,
        hashtags: ['#ai'],
        originalUrl: 'https://example.com',
      },
      {
        content: 'Engaging tweet with question about AI developments? #AI #DevLife https://example.com',
        characterCount: 85,
        hashtags: ['#ai', '#devlife'],
        originalUrl: 'https://example.com',
      },
      {
        content: 'Tweet with too many hashtags #AI #ML #Tech #Dev #Code #Programming https://example.com',
        characterCount: 90,
        hashtags: ['#ai', '#ml', '#tech', '#dev', '#code', '#programming'],
        originalUrl: 'https://example.com',
      },
    ];

    it('should select the best tweet based on scoring', async () => {
      const bestTweet = service.selectBestTweet(mockTweets);

      expect(bestTweet).not.toBeNull();
      // Should prefer the tweet with question mark and optimal hashtag count
      expect(bestTweet!.content).toContain('question');
    });

    it('should return null for empty array', () => {
      const result = service.selectBestTweet([]);
      expect(result).toBeNull();
    });

    it('should return single tweet if only one provided', () => {
      const result = service.selectBestTweet([mockTweets[0]]);
      expect(result).toBe(mockTweets[0]);
    });
  });

  describe('private methods', () => {
    it('should score tweets correctly', () => {
      const serviceAny = service as any;

      const goodTweet = {
        content: 'Great new AI update! What do you think? #AI #DevLife https://example.com',
        characterCount: 120,
        hashtags: ['#ai', '#devlife'],
        originalUrl: 'https://example.com',
      };

      const score = serviceAny.scoreTweet(goodTweet);
      expect(score).toBeGreaterThan(0);
    });

    it('should select relevant hashtags', () => {
      const serviceAny = service as any;

      expect(serviceAny.selectRelevantHashtag('artificial intelligence')).toBe('#AI');
      expect(serviceAny.selectRelevantHashtag('machine learning')).toBe('#MachineLearning');
      expect(serviceAny.selectRelevantHashtag('programming')).toBe('#Programming');
      expect(serviceAny.selectRelevantHashtag('web development')).toBe('#WebDev');
      expect(serviceAny.selectRelevantHashtag('random topic')).toBe('#TechNews');
    });

    it('should trim tweets to correct length', () => {
      const serviceAny = service as any;
      const longTweet = 'A'.repeat(300);
      const url = 'https://example.com';

      const trimmed = serviceAny.trimTweetToLength(longTweet, url);
      expect(trimmed.length).toBeLessThanOrEqual(280);
      expect(trimmed).toContain(url);
    });
  });
});
