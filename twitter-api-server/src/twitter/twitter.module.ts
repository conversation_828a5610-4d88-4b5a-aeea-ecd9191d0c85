import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TwitterService } from './twitter.service';
import { TwitterController } from './twitter.controller';
import { DatabaseModule } from '../database/database.module';
import { ArticleFetcherService } from './services/article-fetcher.service';
import { ArticleSummarizerService } from './services/article-summarizer.service';
import { TweetGeneratorService } from './services/tweet-generator.service';
import { TweetSchedulerService } from './services/tweet-scheduler.service';

@Module({
  imports: [ConfigModule, DatabaseModule],
  providers: [
    TwitterService,
    ArticleFetcherService,
    ArticleSummarizerService,
    TweetGeneratorService,
    TweetSchedulerService,
  ],
  controllers: [TwitterController],
  exports: [
    TwitterService,
    ArticleFetcherService,
    ArticleSummarizerService,
    TweetGeneratorService,
    TweetSchedulerService,
  ]
})
export class TwitterModule {}
