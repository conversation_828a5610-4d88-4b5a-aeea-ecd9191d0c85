import { Logger } from '@nestjs/common';

export interface RetryOptions {
  maxAttempts: number;
  delayMs: number;
  backoffMultiplier?: number;
  maxDelayMs?: number;
  retryCondition?: (error: any) => boolean;
}

export class RetryUtil {
  private static readonly logger = new Logger(RetryUtil.name);

  /**
   * Retry a function with exponential backoff
   */
  static async withRetry<T>(
    fn: () => Promise<T>,
    options: RetryOptions,
    context?: string
  ): Promise<T> {
    const {
      maxAttempts,
      delayMs,
      backoffMultiplier = 2,
      maxDelayMs = 30000,
      retryCondition = () => true
    } = options;

    let lastError: any;
    let currentDelay = delayMs;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await fn();
        
        if (attempt > 1) {
          this.logger.log(`${context || 'Operation'} succeeded on attempt ${attempt}`);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        this.logger.warn(
          `${context || 'Operation'} failed on attempt ${attempt}/${maxAttempts}: ${error.message}`
        );

        // Check if we should retry this error
        if (!retryCondition(error)) {
          this.logger.error(`${context || 'Operation'} failed with non-retryable error: ${error.message}`);
          throw error;
        }

        // Don't delay on the last attempt
        if (attempt < maxAttempts) {
          this.logger.log(`Retrying in ${currentDelay}ms...`);
          await this.delay(currentDelay);
          
          // Exponential backoff with max delay cap
          currentDelay = Math.min(currentDelay * backoffMultiplier, maxDelayMs);
        }
      }
    }

    this.logger.error(`${context || 'Operation'} failed after ${maxAttempts} attempts`);
    throw lastError;
  }

  /**
   * Retry specifically for HTTP requests
   */
  static async withHttpRetry<T>(
    fn: () => Promise<T>,
    context?: string
  ): Promise<T> {
    return this.withRetry(
      fn,
      {
        maxAttempts: 3,
        delayMs: 1000,
        backoffMultiplier: 2,
        maxDelayMs: 10000,
        retryCondition: (error) => {
          // Retry on network errors, timeouts, and 5xx status codes
          if (error.code === 'ECONNRESET' || 
              error.code === 'ETIMEDOUT' || 
              error.code === 'ENOTFOUND') {
            return true;
          }
          
          if (error.response?.status >= 500) {
            return true;
          }
          
          // Don't retry on 4xx errors (client errors)
          if (error.response?.status >= 400 && error.response?.status < 500) {
            return false;
          }
          
          return true;
        }
      },
      context
    );
  }

  /**
   * Retry specifically for OpenAI API calls
   */
  static async withOpenAIRetry<T>(
    fn: () => Promise<T>,
    context?: string
  ): Promise<T> {
    return this.withRetry(
      fn,
      {
        maxAttempts: 3,
        delayMs: 2000,
        backoffMultiplier: 2,
        maxDelayMs: 15000,
        retryCondition: (error) => {
          // Retry on rate limits, server errors, and network issues
          if (error.status === 429) { // Rate limit
            return true;
          }
          
          if (error.status >= 500) { // Server errors
            return true;
          }
          
          if (error.code === 'ECONNRESET' || 
              error.code === 'ETIMEDOUT') {
            return true;
          }
          
          // Don't retry on authentication or quota errors
          if (error.status === 401 || error.status === 403) {
            return false;
          }
          
          return true;
        }
      },
      context
    );
  }

  /**
   * Retry specifically for Twitter API calls
   */
  static async withTwitterRetry<T>(
    fn: () => Promise<T>,
    context?: string
  ): Promise<T> {
    return this.withRetry(
      fn,
      {
        maxAttempts: 3,
        delayMs: 5000, // Twitter has stricter rate limits
        backoffMultiplier: 2,
        maxDelayMs: 30000,
        retryCondition: (error) => {
          // Retry on rate limits and server errors
          if (error.code === 88 || // Rate limit exceeded
              error.code === 130 || // Over capacity
              error.code === 131) { // Internal error
            return true;
          }
          
          // Retry on HTTP 5xx errors
          if (error.status >= 500) {
            return true;
          }
          
          // Don't retry on authentication errors
          if (error.code === 32 || // Authentication failed
              error.code === 89 || // Invalid or expired token
              error.status === 401 || 
              error.status === 403) {
            return false;
          }
          
          // Don't retry on duplicate tweets
          if (error.code === 187) { // Duplicate tweet
            return false;
          }
          
          return true;
        }
      },
      context
    );
  }

  /**
   * Circuit breaker pattern for repeated failures
   */
  static createCircuitBreaker<T>(
    fn: () => Promise<T>,
    options: {
      failureThreshold: number;
      resetTimeoutMs: number;
      monitorWindowMs: number;
    }
  ) {
    let failures = 0;
    let lastFailureTime = 0;
    let state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

    return async (): Promise<T> => {
      const now = Date.now();

      // Reset failure count if monitor window has passed
      if (now - lastFailureTime > options.monitorWindowMs) {
        failures = 0;
        state = 'CLOSED';
      }

      // If circuit is open, check if we should try again
      if (state === 'OPEN') {
        if (now - lastFailureTime < options.resetTimeoutMs) {
          throw new Error('Circuit breaker is OPEN - too many recent failures');
        }
        state = 'HALF_OPEN';
      }

      try {
        const result = await fn();
        
        // Success - reset circuit breaker
        if (state === 'HALF_OPEN') {
          failures = 0;
          state = 'CLOSED';
          this.logger.log('Circuit breaker reset to CLOSED after successful call');
        }
        
        return result;
      } catch (error) {
        failures++;
        lastFailureTime = now;

        if (failures >= options.failureThreshold) {
          state = 'OPEN';
          this.logger.error(`Circuit breaker opened after ${failures} failures`);
        }

        throw error;
      }
    };
  }

  /**
   * Simple delay utility
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Timeout wrapper for promises
   */
  static withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    timeoutMessage?: string
  ): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(timeoutMessage || `Operation timed out after ${timeoutMs}ms`));
        }, timeoutMs);
      })
    ]);
  }

  /**
   * Batch processing with retry and rate limiting
   */
  static async processBatch<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    options: {
      batchSize?: number;
      delayBetweenBatches?: number;
      retryOptions?: RetryOptions;
    } = {}
  ): Promise<R[]> {
    const {
      batchSize = 5,
      delayBetweenBatches = 1000,
      retryOptions
    } = options;

    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      this.logger.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(items.length / batchSize)}`);
      
      const batchPromises = batch.map(async (item, index) => {
        const processorFn = retryOptions 
          ? () => this.withRetry(() => processor(item), retryOptions, `Batch item ${i + index + 1}`)
          : () => processor(item);
          
        try {
          return await processorFn();
        } catch (error) {
          this.logger.error(`Failed to process batch item ${i + index + 1}:`, error);
          return null; // Return null for failed items
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.filter(result => result !== null));

      // Delay between batches (except for the last batch)
      if (i + batchSize < items.length && delayBetweenBatches > 0) {
        await this.delay(delayBetweenBatches);
      }
    }

    return results;
  }
}
