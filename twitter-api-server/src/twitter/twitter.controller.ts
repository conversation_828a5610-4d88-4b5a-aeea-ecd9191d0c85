import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Logger,
  ValidationPipe,
  UsePipes,
  Query
} from '@nestjs/common';
import { TwitterService } from './twitter.service';
import { DatabaseService } from '../database/database.service';
import { PostTweetDto } from './dto/post-tweet.dto';
import {
  TwitterStatsResponseDto,
  PostTweetResponseDto,
  TokenStatusResponseDto
} from './dto/twitter-response.dto';
import { ArticleFetcherService } from './services/article-fetcher.service';
import { ArticleSummarizerService } from './services/article-summarizer.service';
import { TweetGeneratorService } from './services/tweet-generator.service';
import { TweetSchedulerService } from './services/tweet-scheduler.service';

@Controller('twitter')
export class TwitterController {
  private readonly logger = new Logger(TwitterController.name);

  constructor(
    private readonly twitterService: TwitterService,
    private readonly databaseService: DatabaseService,
    private readonly articleFetcher: ArticleFetcherService,
    private readonly articleSummarizer: ArticleSummarizerService,
    private readonly tweetGenerator: TweetGeneratorService,
    private readonly tweetScheduler: TweetSchedulerService,
  ) {}

  @Post('post')
  @UsePipes(new ValidationPipe({ transform: true }))
  async postTweet(@Body() postTweetDto: PostTweetDto): Promise<PostTweetResponseDto> {
    try {
      this.logger.log(`Posting tweet: ${postTweetDto.content.substring(0, 50)}...`);

      const tweet = await this.twitterService.postTweet(postTweetDto.content);

      return {
        success: true,
        tweet: {
          id: tweet.id,
          text: tweet.text,
          created_at: tweet.created_at
        }
      };
    } catch (error) {
      this.logger.error('Error posting tweet:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to post tweet'
      };
    }
  }

  @Get('stats')
  async getTwitterStats(): Promise<TwitterStatsResponseDto> {
    try {
      // Get current user data from Twitter
      const userData = await this.twitterService.getCurrentUser();

      // Calculate engagement metrics
      const engagementMetrics = await this.twitterService.calculateEngagementMetrics();

      return {
        followers_count: userData.followers_count,
        following_count: userData.following_count,
        total_tweets: userData.tweet_count,
        total_impressions: engagementMetrics.totalImpressions,
        total_engagements: engagementMetrics.totalEngagements,
        engagement_rate: engagementMetrics.engagementRate,
        username: userData.username,
        name: userData.name,
        verified: userData.verified,
        profile_image_url: userData.profile_image_url
      };
    } catch (error) {
      this.logger.error('Error fetching Twitter stats:', error);
      throw new HttpException(
        'Failed to fetch Twitter stats',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('stats')
  async refreshTwitterStats(): Promise<{ success: boolean }> {
    try {
      // Force refresh of Twitter stats
      const userData = await this.twitterService.getCurrentUser();
      const totalImpressions = await this.twitterService.getTotalImpressions();
      const engagementMetrics = await this.twitterService.calculateEngagementMetrics();

      // Update analytics in database
      await this.databaseService.addAnalytics({
        followers_count: userData.followers_count,
        following_count: userData.following_count,
        total_tweets: userData.tweet_count,
        total_impressions: totalImpressions,
        total_engagements: engagementMetrics.totalEngagements
      });

      return { success: true };
    } catch (error) {
      this.logger.error('Error refreshing Twitter stats:', error);
      throw new HttpException(
        'Failed to refresh Twitter stats',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('auth/status')
  async getAuthStatus(): Promise<TokenStatusResponseDto> {
    try {
      return await this.twitterService.getTokenStatus();
    } catch (error) {
      this.logger.error('Error getting auth status:', error);
      throw new HttpException(
        'Failed to get authentication status',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('health')
  async healthCheck(): Promise<{ status: string; database: boolean; tokens: boolean }> {
    try {
      const databaseHealth = await this.databaseService.healthCheck();
      const hasTokens = await this.twitterService.hasValidTokens();

      return {
        status: databaseHealth && hasTokens ? 'healthy' : 'degraded',
        database: databaseHealth,
        tokens: hasTokens
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        database: false,
        tokens: false
      };
    }
  }

  // Twitter Bot Endpoints

  @Post('bot/fetch-article')
  async fetchArticle(@Body() body: { url: string }) {
    try {
      this.logger.log(`Fetching article from: ${body.url}`);
      const article = await this.articleFetcher.fetchAndTrimArticle(body.url);

      if (!article) {
        throw new HttpException('Failed to fetch article', HttpStatus.BAD_REQUEST);
      }

      return {
        success: true,
        article
      };
    } catch (error) {
      this.logger.error('Error fetching article:', error);
      throw new HttpException(
        error.message || 'Failed to fetch article',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('bot/summarize-article')
  async summarizeArticle(@Body() body: { url: string }) {
    try {
      this.logger.log(`Summarizing article from: ${body.url}`);

      // First fetch the article
      const article = await this.articleFetcher.fetchAndTrimArticle(body.url);
      if (!article) {
        throw new HttpException('Failed to fetch article', HttpStatus.BAD_REQUEST);
      }

      // Then summarize it
      const summary = await this.articleSummarizer.summarizeWithGPT3(article);
      if (!summary) {
        throw new HttpException('Failed to summarize article', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      return {
        success: true,
        article,
        summary
      };
    } catch (error) {
      this.logger.error('Error summarizing article:', error);
      throw new HttpException(
        error.message || 'Failed to summarize article',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('bot/generate-tweet')
  async generateTweet(@Body() body: { url: string }) {
    try {
      this.logger.log(`Generating tweet for article: ${body.url}`);

      // Fetch article
      const article = await this.articleFetcher.fetchAndTrimArticle(body.url);
      if (!article) {
        throw new HttpException('Failed to fetch article', HttpStatus.BAD_REQUEST);
      }

      // Summarize article
      const summary = await this.articleSummarizer.summarizeWithGPT3(article);
      if (!summary) {
        throw new HttpException('Failed to summarize article', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      // Generate tweet
      const generatedTweet = await this.tweetGenerator.generateTweetWithGPT4(article, summary);
      if (!generatedTweet) {
        throw new HttpException('Failed to generate tweet', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      return {
        success: true,
        article,
        summary,
        tweet: generatedTweet
      };
    } catch (error) {
      this.logger.error('Error generating tweet:', error);
      throw new HttpException(
        error.message || 'Failed to generate tweet',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('bot/generate-and-post-tweet')
  async generateAndPostTweet(@Body() body: { url: string }) {
    try {
      this.logger.log(`Generating and posting tweet for article: ${body.url}`);

      // Fetch article
      const article = await this.articleFetcher.fetchAndTrimArticle(body.url);
      if (!article) {
        throw new HttpException('Failed to fetch article', HttpStatus.BAD_REQUEST);
      }

      // Summarize article
      const summary = await this.articleSummarizer.summarizeWithGPT3(article);
      if (!summary) {
        throw new HttpException('Failed to summarize article', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      // Generate tweet
      const generatedTweet = await this.tweetGenerator.generateTweetWithGPT4(article, summary);
      if (!generatedTweet) {
        throw new HttpException('Failed to generate tweet', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      // Post tweet
      const postedTweet = await this.twitterService.postTweet(generatedTweet.content);

      return {
        success: true,
        article,
        summary,
        generatedTweet,
        postedTweet
      };
    } catch (error) {
      this.logger.error('Error generating and posting tweet:', error);
      throw new HttpException(
        error.message || 'Failed to generate and post tweet',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('bot/scheduler/start')
  async startScheduler(@Body() body?: { cronExpression?: string; maxTweetsPerRun?: number }) {
    try {
      this.logger.log('Starting tweet scheduler...');

      await this.tweetScheduler.startScheduler({
        enabled: true,
        cronExpression: body?.cronExpression || '0 */2 * * *',
        maxTweetsPerRun: body?.maxTweetsPerRun || 1,
        feedUrls: [
          'https://techcrunch.com/feed/',
          'https://www.theverge.com/rss/index.xml',
          'https://news.ycombinator.com/rss'
        ]
      });

      return {
        success: true,
        message: 'Tweet scheduler started successfully'
      };
    } catch (error) {
      this.logger.error('Error starting scheduler:', error);
      throw new HttpException(
        error.message || 'Failed to start scheduler',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('bot/scheduler/stop')
  async stopScheduler() {
    try {
      this.logger.log('Stopping tweet scheduler...');
      this.tweetScheduler.stopAllSchedulers();

      return {
        success: true,
        message: 'Tweet scheduler stopped successfully'
      };
    } catch (error) {
      this.logger.error('Error stopping scheduler:', error);
      throw new HttpException(
        error.message || 'Failed to stop scheduler',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('bot/scheduler/status')
  async getSchedulerStatus() {
    try {
      const status = this.tweetScheduler.getSchedulerStatus();
      return {
        success: true,
        status
      };
    } catch (error) {
      this.logger.error('Error getting scheduler status:', error);
      throw new HttpException(
        'Failed to get scheduler status',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('bot/scheduler/trigger')
  async triggerManualRun() {
    try {
      this.logger.log('Triggering manual tweet generation...');

      await this.tweetScheduler.triggerManualRun({
        maxTweetsPerRun: 1
      });

      return {
        success: true,
        message: 'Manual tweet generation triggered successfully'
      };
    } catch (error) {
      this.logger.error('Error triggering manual run:', error);
      throw new HttpException(
        error.message || 'Failed to trigger manual run',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
