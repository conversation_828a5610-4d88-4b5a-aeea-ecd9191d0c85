# Twitter API Configuration
TWITTER_CLIENT_ID=your_twitter_client_id_here
TWITTER_CLIENT_SECRET=your_twitter_client_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_REFRESH_TOKEN=your_twitter_refresh_token_here

# Legacy Twitter API v1.1 (if needed)
TWITTER_APP_KEY=your_twitter_app_key_here
TWITTER_APP_SECRET=your_twitter_app_secret_here
TWITTER_ACCESS_SECRET=your_twitter_access_secret_here
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Twitter Bot Configuration
TWITTER_BOT_ENABLED=false
TWITTER_BOT_CRON_SCHEDULE=0 */2 * * *
TWITTER_BOT_MAX_TWEETS_PER_RUN=1
TWITTER_BOT_FEED_URLS=https://techcrunch.com/feed/,https://www.theverge.com/rss/index.xml,https://news.ycombinator.com/rss

# Application Configuration
NODE_ENV=development
PORT=3001
LOG_LEVEL=info

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Configuration
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:3000

# Monitoring and Analytics
ENABLE_ANALYTICS=true
ANALYTICS_RETENTION_DAYS=30

# Feature Flags
ENABLE_TWEET_VARIATIONS=true
ENABLE_CONTENT_FILTERING=true
ENABLE_ENGAGEMENT_TRACKING=true
