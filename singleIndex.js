// ai-twitter-bot/index.mjs
import dotenv from 'dotenv';
dotenv.config();

import Parser from 'rss-parser';
import fetch from 'node-fetch';
import unfluff from 'unfluff';
import { TwitterApi } from 'twitter-api-v2';
import OpenAI from 'openai';
import fs from 'fs';

const parser = new Parser();

console.log('Attempting tweet with:', {
      clientId: process.env.TWITTER_CLIENT_ID,
  clientSecret: process.env.TWITTER_CLIENT_SECRET,
  accessToken: process.env.TWITTER_ACCESS_TOKEN,
  refreshToken: process.env.TWITTER_REFRESH_TOKEN,
})
// OAuth 2.0 User Context setup
const twitterClient = new TwitterApi({
  clientId: process.env.TWITTER_CLIENT_ID,
  clientSecret: process.env.TWITTER_CLIENT_SECRET,
  accessToken: process.env.TWITTER_ACCESS_TOKEN,
  refreshToken: process.env.TWITTER_REFRESH_TOKEN,
});

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

const testFeed = 'https://techcrunch.com/feed/';

async function extractContent(url) {
  try {
    const res = await fetch(url);
    const html = await res.text();
    const article = unfluff(html);
    return article.text;
  } catch (err) {
    console.error(`Failed to fetch content from ${url}:`, err);
    return null;
  }
}

async function testSingleTweet() {
  const feed = await parser.parseURL(testFeed);
  const entry = feed.items[0];
  const content = await extractContent(entry.link);
  if (!content) return;

  const prompt = `Here is a tech article:\n\nTitle: ${entry.title}\n\nContent: ${content.slice(0, 3000)}\n\nWrite a tweet (max 280 characters) summarizing the article with a clear, smart POV. Use an analytical and insightful tone.`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 200,
    });

    const tweet = completion.choices[0].message.content.trim();
    console.log('Generated Tweet:', tweet);

    // Refresh token and tweet using OAuth2 user context
    const { client: rwClient, accessToken, refreshToken } = await twitterClient.refreshOAuth2Token(process.env.TWITTER_REFRESH_TOKEN);
    console.log('New Access Token:', accessToken);
    console.log('New Refresh Token:', refreshToken);

    await rwClient.v2.tweet(tweet);
    console.log(`✅ Posted: ${entry.title}`);
  } catch (err) {
    console.error(`❌ Error posting tweet for ${entry.link}`, err);
  }
}

// Only run the test tweet
testSingleTweet();
