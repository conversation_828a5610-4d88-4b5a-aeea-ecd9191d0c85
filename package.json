{"name": "agentdevelopment", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@extractus/article-extractor": "^8.0.19", "dotenv": "^17.0.0", "express": "^5.1.0", "node-cron": "^4.1.1", "node-fetch": "^3.3.2", "openai": "^5.8.2", "rss-parser": "^3.13.0", "twitter-api-v2": "^1.24.0", "unfluff": "^3.2.0"}}